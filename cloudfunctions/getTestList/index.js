// 获取测试列表云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { 
      category = '', 
      type = '', 
      page = 1, 
      pageSize = 10, 
      sortBy = 'hot',
      keyword = ''
    } = event

    // 构建查询条件
    let query = db.collection('tests').where({
      status: 'published'
    })

    // 分类筛选
    if (category) {
      query = query.where({
        category: category
      })
    }

    // 类型筛选
    if (type) {
      query = query.where({
        type: type
      })
    }

    // 关键词搜索
    if (keyword) {
      query = query.where({
        title: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      })
    }

    // 排序
    let orderBy = 'createdAt'
    let order = 'desc'
    
    switch (sortBy) {
      case 'hot':
        orderBy = 'viewCount'
        order = 'desc'
        break
      case 'new':
        orderBy = 'createdAt'
        order = 'desc'
        break
      case 'popular':
        orderBy = 'completionCount'
        order = 'desc'
        break
      case 'share':
        orderBy = 'shareCount'
        order = 'desc'
        break
    }

    // 分页查询
    const skip = (page - 1) * pageSize
    const result = await query
      .orderBy(orderBy, order)
      .skip(skip)
      .limit(pageSize)
      .field({
        title: true,
        description: true,
        type: true,
        category: true,
        difficulty: true,
        questionCount: true,
        estimatedTime: true,
        coverImage: true,
        tags: true,
        isHot: true,
        isRecommended: true,
        viewCount: true,
        shareCount: true,
        likeCount: true,
        completionCount: true,
        createdAt: true
      })
      .get()

    // 获取总数
    const countResult = await query.count()

    return {
      success: true,
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        pageSize: pageSize,
        hasMore: skip + pageSize < countResult.total
      }
    }

  } catch (error) {
    console.error('获取测试列表失败:', error)
    return {
      success: false,
      message: '获取测试列表失败',
      error: error.message
    }
  }
}
