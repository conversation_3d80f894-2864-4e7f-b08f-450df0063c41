// brain-teaser.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: false,
    loadingText: '',
    currentQuestion: null,
    userAnswer: '',
    hasAnswered: false,
    answerResult: null,
    showHint: false,
    hintUsed: false,
    difficulty: '',
    category: '',
    userStats: {
      totalAnswered: 0,
      correctCount: 0,
      totalPoints: 0
    },
    answeredQuestions: [] // 已答过的题目ID
  },

  onLoad() {
    this.loadUserStats()
    this.loadQuestion()
  },

  onShow() {
    // 刷新用户统计
    this.loadUserStats()
  },

  /**
   * 加载用户统计
   */
  async loadUserStats() {
    try {
      // 这里应该调用API获取用户统计
      // const stats = await api.getUserBrainStats()
      // this.setData({ userStats: stats })
      
      // 模拟数据
      this.setData({
        userStats: {
          totalAnswered: 15,
          correctCount: 12,
          totalPoints: 45
        }
      })
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  },

  /**
   * 加载题目
   */
  async loadQuestion() {
    try {
      this.setData({ 
        loading: true,
        loadingText: '加载题目中...'
      })

      const params = {
        difficulty: this.data.difficulty,
        category: this.data.category,
        excludeIds: this.data.answeredQuestions
      }

      const result = await api.getBrainTeaserQuestion(params)
      
      this.setData({
        currentQuestion: result.question,
        hasAnswered: result.hasAnswered,
        answerResult: result.userRecord,
        userAnswer: result.userRecord ? result.userRecord.userAnswer : '',
        showHint: result.userRecord ? result.userRecord.hintsUsed > 0 : false,
        hintUsed: result.userRecord ? result.userRecord.hintsUsed > 0 : false
      })

    } catch (error) {
      console.error('加载题目失败:', error)
      util.showError('加载题目失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 答案输入
   */
  onAnswerInput(e) {
    this.setData({
      userAnswer: e.detail.value
    })
  },

  /**
   * 显示提示
   */
  onShowHint() {
    this.setData({
      showHint: true,
      hintUsed: true
    })
  },

  /**
   * 提交答案
   */
  async onSubmitAnswer() {
    if (!this.data.userAnswer.trim()) {
      util.showError('请输入答案')
      return
    }

    try {
      this.setData({ 
        loading: true,
        loadingText: '提交中...'
      })

      const result = await api.submitBrainTeaserAnswer(
        this.data.currentQuestion._id,
        this.data.userAnswer.trim(),
        this.data.hintUsed ? 1 : 0,
        0 // 答题时间，可以计算
      )

      this.setData({
        hasAnswered: true,
        answerResult: result
      })

      // 添加到已答题目列表
      const answeredQuestions = [...this.data.answeredQuestions, this.data.currentQuestion._id]
      this.setData({ answeredQuestions })

      // 更新用户统计
      this.updateUserStats(result)

      // 显示结果提示
      if (result.isCorrect) {
        util.showSuccess(`回答正确！获得${result.pointsEarned}积分`)
      } else {
        util.showError('回答错误，再接再厉！')
      }

    } catch (error) {
      console.error('提交答案失败:', error)
      util.showError('提交失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 更新用户统计
   */
  updateUserStats(result) {
    const userStats = { ...this.data.userStats }
    userStats.totalAnswered += 1
    if (result.isCorrect) {
      userStats.correctCount += 1
    }
    userStats.totalPoints += result.pointsEarned

    this.setData({ userStats })
  },

  /**
   * 下一题
   */
  onNextQuestion() {
    this.resetQuestionState()
    this.loadQuestion()
  },

  /**
   * 随机题目
   */
  onRandomQuestion() {
    // 清空筛选条件，获取随机题目
    this.setData({
      difficulty: '',
      category: ''
    })
    this.resetQuestionState()
    this.loadQuestion()
  },

  /**
   * 重置题目状态
   */
  resetQuestionState() {
    this.setData({
      userAnswer: '',
      hasAnswered: false,
      answerResult: null,
      showHint: false,
      hintUsed: false
    })
  },

  /**
   * 筛选条件点击
   */
  onFilterTap(e) {
    const { type, value } = e.currentTarget.dataset
    
    this.setData({
      [type]: value
    })
    
    this.resetQuestionState()
    this.loadQuestion()
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    const question = this.data.currentQuestion
    if (question) {
      return {
        title: `脑筋急转弯：${question.question}`,
        path: '/pages/brain-teaser/brain-teaser',
        imageUrl: '/images/share-brain-teaser.jpg'
      }
    }
    
    return {
      title: '脑筋急转弯 - 挑战你的智慧',
      path: '/pages/brain-teaser/brain-teaser',
      imageUrl: '/images/share-brain-teaser.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '脑筋急转弯 - 挑战你的智慧',
      imageUrl: '/images/share-timeline.jpg'
    }
  }
})
