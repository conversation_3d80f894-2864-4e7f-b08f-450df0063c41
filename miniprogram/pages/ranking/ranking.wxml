<!--ranking.wxml-->
<view class="container">
  <!-- 排行榜类型切换 -->
  <view class="tabs-section">
    <view class="tab-item {{currentTab === 'total' ? 'active' : ''}}" bindtap="onTabTap" data-tab="total">
      总积分榜
    </view>
    <view class="tab-item {{currentTab === 'weekly' ? 'active' : ''}}" bindtap="onTabTap" data-tab="weekly">
      周榜
    </view>
    <view class="tab-item {{currentTab === 'monthly' ? 'active' : ''}}" bindtap="onTabTap" data-tab="monthly">
      月榜
    </view>
  </view>

  <!-- 我的排名 -->
  <view class="my-rank-section" wx:if="{{myRank}}">
    <view class="my-rank-card">
      <view class="rank-info">
        <view class="rank-number">第{{myRank.rank}}名</view>
        <view class="rank-points">{{myRank.points}}积分</view>
      </view>
      <view class="user-info">
        <image class="user-avatar" src="{{myRank.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-name">{{myRank.nickName}}</view>
      </view>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <view class="ranking-list">
    <view class="ranking-item" wx:for="{{rankingList}}" wx:key="_id">
      <view class="rank-badge rank-{{index + 1}}">{{index + 1}}</view>
      <image class="user-avatar" src="{{item.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-info">
        <view class="user-name">{{item.nickName}}</view>
        <view class="user-stats">{{item.points}}积分 · {{item.completedTests}}个测试</view>
      </view>
      <view class="user-level">Lv.{{item.level}}</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && rankingList.length === 0}}">
    <view class="empty-icon">🏆</view>
    <view class="empty-title">暂无排行数据</view>
    <view class="empty-desc">快去完成测试获得积分吧！</view>
  </view>
</view>
