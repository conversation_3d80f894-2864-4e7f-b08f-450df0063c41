/* ranking.wxss */

.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 标签切换 */
.tabs-section {
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  margin: 30rpx;
  border-radius: 50rpx;
  padding: 8rpx;
  backdrop-filter: blur(10rpx);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 42rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  font-weight: 500;
}

/* 我的排名 */
.my-rank-section {
  margin: 0 30rpx 30rpx;
}

.my-rank-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.rank-info {
  color: white;
}

.rank-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.rank-points {
  font-size: 24rpx;
  opacity: 0.9;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
  color: white;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
}

/* 排行榜列表 */
.ranking-list {
  background: white;
  margin: 0 30rpx;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item:active {
  background-color: #f8f9fa;
}

.rank-badge {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(192, 192, 192, 0.3);
}

.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: #f0f0f0;
  color: #666;
}

.ranking-item .user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.ranking-item .user-info {
  flex: 1;
  color: #333;
}

.ranking-item .user-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #333;
}

.user-stats {
  font-size: 24rpx;
  color: #999;
}

.user-level {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white;
  margin: 0 30rpx;
  border-radius: 20rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: white;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
