/* test-result.wxss */

.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 结果头部 */
.result-header {
  text-align: center;
  padding: 60rpx 30rpx 40rpx;
  color: white;
}

.result-celebration {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.result-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 结果卡片 */
.result-card {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.result-image-section {
  position: relative;
  text-align: center;
  padding: 40rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.result-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 20rpx;
}

.result-percentage {
  position: absolute;
  top: 60rpx;
  right: 60rpx;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 15rpx 25rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.result-info {
  padding: 40rpx;
}

.result-type {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 25rpx;
}

.result-description {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 30rpx;
}

/* 特质标签 */
.traits-section {
  margin-bottom: 30rpx;
}

.traits-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.traits-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.trait-tag {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 建议区域 */
.advice-section {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 25rpx;
}

.advice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.advice-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 兼容性分析 */
.compatibility-section {
  margin: 0 30rpx 30rpx;
}

.compatibility-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.compatibility-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

.compatibility-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.compatibility-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.compatibility-label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.compatibility-types {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  flex: 1;
}

.type-tag {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666;
}

.compatibility-item.best .type-tag {
  background: #e8f5e8;
  color: #52c41a;
}

.compatibility-item.good .type-tag {
  background: #fff7e6;
  color: #fa8c16;
}

/* 分数详情 */
.scores-section {
  margin: 0 30rpx 30rpx;
}

.scores-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.scores-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

.scores-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.score-label {
  font-size: 26rpx;
  color: #333;
  width: 100rpx;
  flex-shrink: 0;
}

.score-bar {
  flex: 1;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8rpx;
  transition: width 0.5s ease;
}

.score-value {
  font-size: 24rpx;
  color: #666;
  width: 60rpx;
  text-align: right;
  flex-shrink: 0;
}

.scores-toggle {
  text-align: center;
  font-size: 26rpx;
  color: #667eea;
  margin-top: 20rpx;
  padding: 10rpx;
}

/* 操作按钮 */
.action-section {
  padding: 0 30rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-buttons .btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.btn-icon {
  font-size: 24rpx;
}

.secondary-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.secondary-actions .btn {
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 26rpx;
}

.btn-outline {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 海报弹窗 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.poster-content {
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.poster-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.poster-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.poster-close {
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
}

.poster-preview {
  padding: 30rpx;
  text-align: center;
}

.poster-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 16rpx;
}

.poster-loading {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  color: #666;
}

.poster-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.poster-actions .btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

/* 好友匹配 */
.friend-match-section {
  margin: 0 30rpx 30rpx;
}

.friend-match-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  color: white;
  backdrop-filter: blur(10rpx);
}

.friend-match-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.friend-match-desc {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 25rpx;
}

/* 推荐测试 */
.recommend-section {
  margin: 0 30rpx;
}

.recommend-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 25rpx;
}

.recommend-list {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
  padding-bottom: 10rpx;
}

.recommend-item {
  flex-shrink: 0;
  width: 280rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.recommend-cover {
  width: 100%;
  height: 200rpx;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recommend-count {
  font-size: 22rpx;
  color: #999;
}
