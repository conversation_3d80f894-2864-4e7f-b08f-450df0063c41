// 获取测试题目云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { testId } = event

    if (!testId) {
      return {
        success: false,
        message: '测试ID不能为空'
      }
    }

    // 获取测试信息
    const testResult = await db.collection('tests').doc(testId).get()
    
    if (!testResult.data) {
      return {
        success: false,
        message: '测试不存在'
      }
    }

    const test = testResult.data

    // 检查测试状态
    if (test.status !== 'published') {
      return {
        success: false,
        message: '测试暂未发布'
      }
    }

    // 获取题目列表
    const questionsResult = await db.collection('questions')
      .where({
        testId: testId
      })
      .orderBy('questionNumber', 'asc')
      .get()

    // 处理题目数据，移除答案相关信息
    const questions = questionsResult.data.map(question => {
      const processedOptions = question.options.map(option => ({
        id: option.id,
        text: option.text,
        image: option.image || ''
      }))

      return {
        _id: question._id,
        questionNumber: question.questionNumber,
        title: question.title,
        type: question.type,
        options: processedOptions,
        explanation: question.explanation || ''
      }
    })

    // 更新测试浏览量
    await db.collection('tests').doc(testId).update({
      data: {
        viewCount: db.command.inc(1)
      }
    })

    return {
      success: true,
      data: {
        test: {
          _id: test._id,
          title: test.title,
          description: test.description,
          type: test.type,
          category: test.category,
          difficulty: test.difficulty,
          questionCount: test.questionCount,
          estimatedTime: test.estimatedTime,
          coverImage: test.coverImage,
          tags: test.tags
        },
        questions: questions
      }
    }

  } catch (error) {
    console.error('获取测试题目失败:', error)
    return {
      success: false,
      message: '获取测试题目失败',
      error: error.message
    }
  }
}
