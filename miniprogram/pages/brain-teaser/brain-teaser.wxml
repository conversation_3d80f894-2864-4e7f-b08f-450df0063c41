<!--brain-teaser.wxml-->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stat-item">
        <view class="stat-number">{{userStats.totalAnswered || 0}}</view>
        <view class="stat-label">已答题数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userStats.correctCount || 0}}</view>
        <view class="stat-label">答对题数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{userStats.totalPoints || 0}}</view>
        <view class="stat-label">获得积分</view>
      </view>
    </view>
  </view>

  <!-- 题目卡片 -->
  <view class="question-section" wx:if="{{currentQuestion}}">
    <view class="question-card">
      <view class="question-header">
        <view class="question-category">{{currentQuestion.category}}</view>
        <view class="question-difficulty difficulty-{{currentQuestion.difficulty}}">
          {{currentQuestion.difficulty === 'easy' ? '简单' : currentQuestion.difficulty === 'medium' ? '中等' : '困难'}}
        </view>
      </view>
      
      <view class="question-content">
        <view class="question-title">{{currentQuestion.question}}</view>
        
        <!-- 提示按钮 -->
        <view class="hints-section" wx:if="{{currentQuestion.hints && currentQuestion.hints.length > 0}}">
          <button class="hint-btn" bindtap="onShowHint" disabled="{{hintUsed}}">
            {{hintUsed ? '已使用提示' : '💡 查看提示'}}
          </button>
          <view class="hint-text" wx:if="{{showHint}}">
            提示：{{currentQuestion.hints[0]}}
          </view>
        </view>
      </view>

      <!-- 答题区域 -->
      <view class="answer-section" wx:if="{{!hasAnswered}}">
        <view class="answer-input-wrapper">
          <input class="answer-input" 
                 placeholder="请输入你的答案..." 
                 value="{{userAnswer}}" 
                 bindinput="onAnswerInput"
                 bindconfirm="onSubmitAnswer" />
        </view>
        <button class="submit-btn btn btn-primary" 
                bindtap="onSubmitAnswer" 
                disabled="{{!userAnswer.trim()}}">
          提交答案
        </button>
      </view>

      <!-- 答案结果 -->
      <view class="result-section" wx:if="{{hasAnswered}}">
        <view class="result-status {{answerResult.isCorrect ? 'correct' : 'wrong'}}">
          <view class="result-icon">{{answerResult.isCorrect ? '✅' : '❌'}}</view>
          <view class="result-text">{{answerResult.isCorrect ? '回答正确！' : '回答错误'}}</view>
        </view>
        
        <view class="correct-answer">
          <view class="answer-label">正确答案：</view>
          <view class="answer-text">{{answerResult.correctAnswer}}</view>
        </view>
        
        <view class="explanation" wx:if="{{answerResult.explanation}}">
          <view class="explanation-label">解释：</view>
          <view class="explanation-text">{{answerResult.explanation}}</view>
        </view>
        
        <view class="points-earned" wx:if="{{answerResult.pointsEarned > 0}}">
          🎉 获得 {{answerResult.pointsEarned}} 积分！
        </view>
      </view>

      <!-- 题目统计 -->
      <view class="question-stats">
        <view class="stats-item">
          <text class="stats-icon">👀</text>
          <text class="stats-text">{{currentQuestion.stats.viewCount}}人看过</text>
        </view>
        <view class="stats-item">
          <text class="stats-icon">✅</text>
          <text class="stats-text">正确率 {{currentQuestion.stats.correctRate}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="btn btn-secondary btn-large" bindtap="onNextQuestion">
      {{hasAnswered ? '下一题' : '跳过此题'}}
    </button>
    <button class="btn btn-primary btn-large" bindtap="onRandomQuestion">
      🎲 随机题目
    </button>
  </view>

  <!-- 筛选选项 -->
  <view class="filter-section">
    <view class="filter-title">筛选条件</view>
    <view class="filter-options">
      <view class="filter-group">
        <view class="filter-label">难度：</view>
        <view class="filter-tags">
          <text class="filter-tag {{difficulty === '' ? 'active' : ''}}" bindtap="onFilterTap" data-type="difficulty" data-value="">全部</text>
          <text class="filter-tag {{difficulty === 'easy' ? 'active' : ''}}" bindtap="onFilterTap" data-type="difficulty" data-value="easy">简单</text>
          <text class="filter-tag {{difficulty === 'medium' ? 'active' : ''}}" bindtap="onFilterTap" data-type="difficulty" data-value="medium">中等</text>
          <text class="filter-tag {{difficulty === 'hard' ? 'active' : ''}}" bindtap="onFilterTap" data-type="difficulty" data-value="hard">困难</text>
        </view>
      </view>
      
      <view class="filter-group">
        <view class="filter-label">分类：</view>
        <view class="filter-tags">
          <text class="filter-tag {{category === '' ? 'active' : ''}}" bindtap="onFilterTap" data-type="category" data-value="">全部</text>
          <text class="filter-tag {{category === 'logic' ? 'active' : ''}}" bindtap="onFilterTap" data-type="category" data-value="logic">逻辑</text>
          <text class="filter-tag {{category === 'math' ? 'active' : ''}}" bindtap="onFilterTap" data-type="category" data-value="math">数学</text>
          <text class="filter-tag {{category === 'word' ? 'active' : ''}}" bindtap="onFilterTap" data-type="category" data-value="word">文字</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>{{loadingText}}</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && !currentQuestion}}">
    <view class="empty-icon">🤔</view>
    <view class="empty-title">暂无题目</view>
    <view class="empty-desc">换个筛选条件试试吧</view>
  </view>
</view>
