// 获取脑筋急转弯题目云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { 
      difficulty = '',
      category = '',
      excludeIds = []
    } = event

    const { OPENID } = cloud.getWXContext()

    // 构建查询条件
    let query = db.collection('brain_teasers')

    if (difficulty) {
      query = query.where({
        difficulty: difficulty
      })
    }

    if (category) {
      query = query.where({
        category: category
      })
    }

    // 排除已答过的题目
    if (excludeIds.length > 0) {
      query = query.where({
        _id: db.command.nin(excludeIds)
      })
    }

    // 随机获取一道题目
    const countResult = await query.count()
    const total = countResult.total

    if (total === 0) {
      return {
        success: false,
        message: '暂无可用题目'
      }
    }

    const randomSkip = Math.floor(Math.random() * total)
    const questionResult = await query
      .skip(randomSkip)
      .limit(1)
      .field({
        question: true,
        difficulty: true,
        category: true,
        hints: true,
        tags: true,
        viewCount: true,
        correctCount: true,
        wrongCount: true
      })
      .get()

    if (questionResult.data.length === 0) {
      return {
        success: false,
        message: '获取题目失败'
      }
    }

    const question = questionResult.data[0]

    // 更新题目浏览量
    await db.collection('brain_teasers').doc(question._id).update({
      data: {
        viewCount: db.command.inc(1)
      }
    })

    // 检查用户是否已经答过这道题
    const userRecordResult = await db.collection('user_brain_records')
      .where({
        userId: OPENID,
        questionId: question._id
      })
      .get()

    const hasAnswered = userRecordResult.data.length > 0
    const userRecord = hasAnswered ? userRecordResult.data[0] : null

    return {
      success: true,
      data: {
        question: {
          _id: question._id,
          question: question.question,
          difficulty: question.difficulty,
          category: question.category,
          hints: question.hints,
          tags: question.tags,
          stats: {
            viewCount: question.viewCount,
            correctCount: question.correctCount,
            wrongCount: question.wrongCount,
            correctRate: question.correctCount + question.wrongCount > 0 
              ? Math.round((question.correctCount / (question.correctCount + question.wrongCount)) * 100)
              : 0
          }
        },
        hasAnswered: hasAnswered,
        userRecord: userRecord ? {
          isCorrect: userRecord.isCorrect,
          userAnswer: userRecord.userAnswer,
          hintsUsed: userRecord.hintsUsed,
          timeSpent: userRecord.timeSpent,
          pointsEarned: userRecord.pointsEarned,
          answeredAt: userRecord.answeredAt
        } : null
      }
    }

  } catch (error) {
    console.error('获取脑筋急转弯题目失败:', error)
    return {
      success: false,
      message: '获取题目失败',
      error: error.message
    }
  }
}
