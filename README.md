# 🎯 趣味测试小程序

一个基于微信小程序原生开发 + 微信云开发的趣味测试平台，包含性格测试、脑筋急转弯、社交分享等功能。

## ✨ 核心特性

### 🧠 丰富的测试类型
- **性格测试**：奇幻生物、MBTI、职场人格等
- **脑筋急转弯**：逻辑推理、文字游戏、数学题目
- **社交匹配**：好友匹配度测试、兼容性分析

### 🎨 精美的用户体验
- **渐变UI设计**：现代化的视觉效果
- **流畅动画**：丰富的交互反馈
- **响应式布局**：适配各种屏幕尺寸

### 📱 完整的功能体系
- **测试系统**：题目展示、答题流程、结果分析
- **积分系统**：答题获得积分、等级提升
- **排行榜**：总榜、周榜、月榜
- **社交分享**：朋友圈分享、好友邀请
- **个性化海报**：自动生成专属测试海报

## 🏗️ 技术架构

### 前端技术栈
- **微信小程序原生开发**
- **WXML + WXSS + JavaScript**
- **组件化设计**
- **响应式布局**

### 后端技术栈
- **微信云开发**
- **云函数 (Node.js)**
- **云数据库 (MongoDB)**
- **云存储**

### 核心云函数
- `getTestList` - 获取测试列表
- `getTestQuestions` - 获取测试题目
- `getTestResult` - 计算测试结果
- `generatePoster` - 生成个性化海报
- `getBrainTeaserQuestion` - 获取脑筋急转弯题目
- `submitBrainTeaserAnswer` - 提交脑筋急转弯答案
- `getRanking` - 获取排行榜
- `recordShare` - 记录分享行为
- `getFriendMatch` - 获取好友匹配度

## 📊 数据库设计

### 核心数据表
- `tests` - 测试信息表
- `questions` - 题目表
- `test_results` - 测试结果表
- `users` - 用户表
- `user_test_records` - 用户测试记录表
- `brain_teasers` - 脑筋急转弯表
- `user_brain_records` - 用户脑筋急转弯记录表
- `share_records` - 分享记录表
- `friend_relations` - 好友关系表

## 🚀 快速开始

### 环境要求
- 微信开发者工具
- 已开通微信云开发
- Node.js 环境

### 部署步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   ```

2. **配置云开发环境**
   - 在微信开发者工具中创建云开发环境
   - 修改 `miniprogram/utils/config.js` 中的环境ID

3. **部署云函数**
   - 右键各云函数文件夹
   - 选择"上传并部署：云端安装依赖"

4. **初始化数据库**
   - 创建对应的数据库集合
   - 导入 `sample-data.json` 中的示例数据

5. **配置云存储**
   - 开启云存储服务
   - 上传默认图片资源

详细部署指南请参考 [deploy-guide.md](./deploy-guide.md)

## 📱 页面结构

```
pages/
├── index/              # 首页
├── test-list/          # 测试列表
├── test-detail/        # 测试详情
├── test-question/      # 答题页面
├── test-result/        # 结果页面
├── brain-teaser/       # 脑筋急转弯
├── ranking/            # 排行榜
├── profile/            # 个人中心
└── privacy/            # 隐私政策
```

## 🎯 核心功能实现

### 测试系统
- **题目展示**：支持单选题、多选题
- **进度跟踪**：实时显示答题进度
- **结果计算**：基于积分算法计算结果
- **结果展示**：个性化结果页面

### 积分系统
- **答题积分**：完成测试获得积分
- **分享奖励**：分享获得额外积分
- **签到奖励**：每日签到获得积分
- **等级系统**：根据积分计算用户等级

### 社交功能
- **分享机制**：支持分享到朋友圈、好友
- **好友匹配**：计算好友间的匹配度
- **邀请系统**：邀请好友获得奖励

## 🔧 自定义配置

### 添加新测试
1. 在 `tests` 集合中添加测试信息
2. 在 `questions` 集合中添加题目
3. 在 `test_results` 集合中添加结果类型

### 修改UI样式
- 全局样式：`miniprogram/app.wxss`
- 页面样式：各页面的 `.wxss` 文件
- 配置文件：`miniprogram/utils/config.js`

### 扩展功能
- 参考现有页面结构
- 在 `app.json` 中注册新页面
- 添加对应的云函数

## 📈 性能优化

### 已实现优化
- **图片懒加载**：列表页面图片懒加载
- **分页加载**：数据分页减少单次请求量
- **本地缓存**：缓存用户数据和测试数据
- **防抖节流**：搜索和按钮点击防抖

### 建议优化
- **CDN加速**：图片资源使用CDN
- **云函数预热**：配置定时触发器预热
- **数据库索引**：为常用查询字段添加索引

## 🛡️ 合规性处理

### 隐私保护
- **明确授权**：用户授权前清晰告知用途
- **数据最小化**：仅收集必要的用户信息
- **安全存储**：使用微信云开发安全存储

### 内容规范
- **娱乐声明**：明确说明测试仅供娱乐
- **内容审核**：避免涉及封建迷信、低俗内容
- **用户协议**：完善的隐私政策和用户协议

## 🔮 未来规划

### 短期目标
- [ ] 更多测试主题
- [ ] 海报模板系统
- [ ] UGC投稿功能
- [ ] 成就系统

### 长期目标
- [ ] AI智能推荐
- [ ] 多人在线测试
- [ ] 数据分析后台
- [ ] 小程序直播

## 📞 技术支持

如有问题，请参考：
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [微信云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- 项目中的代码注释和文档

## 📄 许可证

本项目仅供学习和参考使用，请遵守相关法律法规。

---

**🎉 开始你的趣味测试之旅吧！**

