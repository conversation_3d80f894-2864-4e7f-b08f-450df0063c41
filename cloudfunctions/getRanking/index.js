// 获取排行榜云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { 
      type = 'total', 
      page = 1, 
      pageSize = 20 
    } = event

    const { OPENID } = cloud.getWXContext()

    // 构建查询条件
    let query = db.collection('users')
    let orderField = 'totalPoints'

    switch (type) {
      case 'total':
        orderField = 'totalPoints'
        break
      case 'weekly':
        // 这里可以添加周榜逻辑，需要额外的字段记录周积分
        orderField = 'weeklyPoints'
        break
      case 'monthly':
        // 这里可以添加月榜逻辑，需要额外的字段记录月积分
        orderField = 'monthlyPoints'
        break
      case 'brain':
        // 脑筋急转弯排行榜
        orderField = 'brainPoints'
        break
    }

    // 分页查询排行榜
    const skip = (page - 1) * pageSize
    const rankingResult = await query
      .orderBy(orderField, 'desc')
      .skip(skip)
      .limit(pageSize)
      .field({
        openId: true,
        nickName: true,
        avatarUrl: true,
        totalPoints: true,
        weeklyPoints: true,
        monthlyPoints: true,
        brainPoints: true,
        completedTests: true,
        level: true,
        updatedAt: true
      })
      .get()

    // 获取当前用户排名
    let myRank = null
    if (OPENID) {
      const userResult = await db.collection('users')
        .where({ openId: OPENID })
        .get()

      if (userResult.data.length > 0) {
        const user = userResult.data[0]
        
        // 计算用户排名
        const rankQuery = db.collection('users')
          .where({
            [orderField]: db.command.gt(user[orderField] || 0)
          })
          .count()

        const rankResult = await rankQuery
        const rank = rankResult.total + 1

        myRank = {
          rank: rank,
          points: user[orderField] || 0,
          nickName: user.nickName || '我',
          avatarUrl: user.avatarUrl || '',
          completedTests: user.completedTests || 0,
          level: user.level || 1
        }
      }
    }

    // 处理排行榜数据，添加排名
    const rankingList = rankingResult.data.map((user, index) => ({
      ...user,
      rank: skip + index + 1,
      points: user[orderField] || 0
    }))

    // 获取总数
    const countResult = await query.count()

    return {
      success: true,
      data: {
        list: rankingList,
        myRank: myRank,
        total: countResult.total,
        page: page,
        pageSize: pageSize,
        hasMore: skip + pageSize < countResult.total
      }
    }

  } catch (error) {
    console.error('获取排行榜失败:', error)
    return {
      success: false,
      message: '获取排行榜失败',
      error: error.message
    }
  }
}
