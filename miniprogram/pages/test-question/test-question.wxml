<!--test-question.wxml-->
<view class="container">
  <!-- 顶部进度条 -->
  <view class="progress-section">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progress}}%"></view>
    </view>
    <view class="progress-text">{{currentQuestionIndex + 1}} / {{questions.length}}</view>
  </view>

  <!-- 题目区域 -->
  <view class="question-section" wx:if="{{currentQuestion}}">
    <view class="question-card">
      <view class="question-number">第 {{currentQuestionIndex + 1}} 题</view>
      <view class="question-title">{{currentQuestion.title}}</view>
      
      <!-- 题目图片 -->
      <image class="question-image" wx:if="{{currentQuestion.image}}" src="{{currentQuestion.image}}" mode="aspectFit"></image>
      
      <!-- 选项列表 -->
      <view class="options-list">
        <view class="option-item {{selectedAnswer === option.id ? 'selected' : ''}}" 
              wx:for="{{currentQuestion.options}}" 
              wx:key="id" 
              wx:for-item="option"
              bindtap="onOptionTap" 
              data-option="{{option.id}}">
          <view class="option-label">{{option.id}}</view>
          <view class="option-content">
            <view class="option-text">{{option.text}}</view>
            <image class="option-image" wx:if="{{option.image}}" src="{{option.image}}" mode="aspectFit"></image>
          </view>
          <view class="option-check" wx:if="{{selectedAnswer === option.id}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="onPrevQuestion" disabled="{{currentQuestionIndex === 0}}">
        上一题
      </button>
      <button class="btn btn-primary" bindtap="onNextQuestion" disabled="{{!selectedAnswer}}">
        {{isLastQuestion ? '完成测试' : '下一题'}}
      </button>
    </view>
  </view>

  <!-- 题目导航 -->
  <view class="navigation-section">
    <view class="nav-title">题目导航</view>
    <view class="nav-dots">
      <view class="nav-dot {{index <= currentQuestionIndex ? 'completed' : ''}} {{index === currentQuestionIndex ? 'current' : ''}}"
            wx:for="{{questions}}" 
            wx:key="_id"
            bindtap="onNavDotTap"
            data-index="{{index}}">
        {{index + 1}}
      </view>
    </view>
  </view>

  <!-- 退出确认弹窗 -->
  <view class="modal-overlay" wx:if="{{showExitModal}}" bindtap="onCancelExit">
    <view class="modal-content" catchtap="">
      <view class="modal-title">确认退出测试？</view>
      <view class="modal-desc">退出后当前进度将不会保存</view>
      <view class="modal-buttons">
        <button class="btn btn-secondary" bindtap="onCancelExit">继续测试</button>
        <button class="btn btn-primary" bindtap="onConfirmExit">确认退出</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>{{loadingText}}</text>
  </view>
</view>
