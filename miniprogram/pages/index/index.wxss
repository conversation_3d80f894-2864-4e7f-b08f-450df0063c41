/**index.wxss**/

page {
  background-color: #F8F9FA;
}

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-section {
  margin-bottom: 30rpx;
}

.banner-swiper {
  height: 400rpx;
  border-radius: 0 0 20rpx 20rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 快速入口区域 */
.quick-entry-section {
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.title-icon {
  font-size: 32rpx;
}

.more-btn {
  font-size: 28rpx;
  color: #667eea;
}

.quick-entry-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.quick-entry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.quick-entry-item:active {
  transform: scale(0.95);
}

.entry-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 15rpx;
}

.entry-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.entry-count {
  font-size: 22rpx;
  color: #999;
}

/* 热门测试区域 */
.hot-tests-section {
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-card {
  position: relative;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.test-card:active {
  transform: scale(0.98);
}

.test-cover {
  width: 100%;
  height: 300rpx;
}

.test-info {
  padding: 30rpx;
}

.test-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.test-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.test-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999;
}

.test-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666;
}

.test-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
}

/* 今日推荐区域 */
.recommend-section {
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.recommend-card {
  position: relative;
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.recommend-bg {
  width: 100%;
  height: 100%;
}

.recommend-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
  display: flex;
  align-items: center;
  justify-content: center;
}

.recommend-content {
  text-align: center;
  color: white;
  padding: 40rpx;
}

.recommend-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.recommend-desc {
  font-size: 28rpx;
  margin-bottom: 40rpx;
  opacity: 0.9;
}

.recommend-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
}
