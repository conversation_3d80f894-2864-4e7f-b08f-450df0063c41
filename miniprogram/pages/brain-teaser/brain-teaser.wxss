/* brain-teaser.wxss */

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 顶部统计 */
.stats-section {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card {
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.stat-item {
  flex: 1;
  text-align: center;
  color: white;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 题目卡片 */
.question-section {
  padding: 30rpx;
}

.question-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.question-category {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}

.question-difficulty {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.difficulty-easy {
  background: #e8f5e8;
  color: #52c41a;
}

.difficulty-medium {
  background: #fff7e6;
  color: #fa8c16;
}

.difficulty-hard {
  background: #fff2f0;
  color: #f5222d;
}

.question-content {
  margin-bottom: 30rpx;
}

.question-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 25rpx;
}

/* 提示区域 */
.hints-section {
  margin-bottom: 25rpx;
}

.hint-btn {
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  margin-bottom: 15rpx;
}

.hint-btn:not([disabled]) {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  color: #333;
}

.hint-text {
  background: #fff7e6;
  border-left: 4rpx solid #fa8c16;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 答题区域 */
.answer-section {
  margin-bottom: 30rpx;
}

.answer-input-wrapper {
  margin-bottom: 20rpx;
}

.answer-input {
  width: 100%;
  padding: 25rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  background: #fafafa;
}

.answer-input:focus {
  border-color: #667eea;
  background: white;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 答案结果 */
.result-section {
  margin-bottom: 30rpx;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 25rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.result-status.correct {
  background: #e8f5e8;
  color: #52c41a;
}

.result-status.wrong {
  background: #fff2f0;
  color: #f5222d;
}

.result-icon {
  font-size: 32rpx;
}

.result-text {
  font-size: 28rpx;
  font-weight: 500;
}

.correct-answer {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.answer-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.answer-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.explanation {
  background: #f0f8ff;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.explanation-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.explanation-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.points-earned {
  text-align: center;
  font-size: 28rpx;
  color: #fa8c16;
  font-weight: 500;
  padding: 15rpx;
  background: #fff7e6;
  border-radius: 12rpx;
}

/* 题目统计 */
.question-stats {
  display: flex;
  gap: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stats-icon {
  font-size: 24rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.action-section {
  padding: 0 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
}

.action-section .btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 筛选区域 */
.filter-section {
  margin: 0 30rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #666;
  width: 80rpx;
  flex-shrink: 0;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  flex: 1;
}

.filter-tag {
  padding: 12rpx 24rpx;
  background: #f0f0f0;
  color: #666;
  border-radius: 25rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}
