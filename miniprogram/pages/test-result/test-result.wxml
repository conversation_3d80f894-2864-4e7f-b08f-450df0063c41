<!--test-result.wxml-->
<view class="container">
  <!-- 结果头部 -->
  <view class="result-header">
    <view class="result-celebration">🎉</view>
    <view class="result-title">测试完成！</view>
    <view class="result-subtitle">你的结果是...</view>
  </view>

  <!-- 结果卡片 -->
  <view class="result-card" wx:if="{{resultData}}">
    <view class="result-image-section">
      <image class="result-image" src="{{resultData.result.image}}" mode="aspectFit"></image>
      <view class="result-percentage">{{resultData.result.percentage}}%</view>
    </view>
    
    <view class="result-info">
      <view class="result-type">{{resultData.result.title}}</view>
      <view class="result-description">{{resultData.result.description}}</view>
      
      <!-- 特质标签 -->
      <view class="traits-section" wx:if="{{resultData.result.traits}}">
        <view class="traits-title">你的特质</view>
        <view class="traits-list">
          <text class="trait-tag" wx:for="{{resultData.result.traits}}" wx:key="*this">{{item}}</text>
        </view>
      </view>

      <!-- 建议 -->
      <view class="advice-section" wx:if="{{resultData.result.advice}}">
        <view class="advice-title">💡 给你的建议</view>
        <view class="advice-text">{{resultData.result.advice}}</view>
      </view>
    </view>
  </view>

  <!-- 兼容性分析 -->
  <view class="compatibility-section" wx:if="{{resultData.result.compatibility}}">
    <view class="compatibility-card">
      <view class="compatibility-title">🤝 兼容性分析</view>
      <view class="compatibility-list">
        <view class="compatibility-item best">
          <view class="compatibility-label">最佳搭配</view>
          <view class="compatibility-types">
            <text class="type-tag" wx:for="{{resultData.result.compatibility.best}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
        <view class="compatibility-item good">
          <view class="compatibility-label">不错搭配</view>
          <view class="compatibility-types">
            <text class="type-tag" wx:for="{{resultData.result.compatibility.good}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分数详情 -->
  <view class="scores-section" wx:if="{{showScores}}">
    <view class="scores-card">
      <view class="scores-title">📊 详细得分</view>
      <view class="scores-list">
        <view class="score-item" wx:for="{{scoresList}}" wx:key="type">
          <view class="score-label">{{item.label}}</view>
          <view class="score-bar">
            <view class="score-fill" style="width: {{item.percentage}}%"></view>
          </view>
          <view class="score-value">{{item.score}}</view>
        </view>
      </view>
      <view class="scores-toggle" bindtap="onToggleScores">收起详情</view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="onRetakeTest">
        <text class="btn-icon">🔄</text>
        <text>重新测试</text>
      </button>
      <button class="btn btn-primary" bindtap="onGeneratePoster">
        <text class="btn-icon">📸</text>
        <text>生成海报</text>
      </button>
    </view>
    
    <view class="secondary-actions">
      <button class="btn btn-outline" bindtap="onToggleScores" wx:if="{{!showScores}}">
        查看详细得分
      </button>
      <button class="btn btn-outline" bindtap="onShareResult">
        分享结果
      </button>
      <button class="btn btn-outline" bindtap="onBackToHome">
        返回首页
      </button>
    </view>
  </view>

  <!-- 海报预览 -->
  <view class="poster-modal" wx:if="{{showPosterModal}}" bindtap="onClosePosterModal">
    <view class="poster-content" catchtap="">
      <view class="poster-header">
        <view class="poster-title">你的专属海报</view>
        <view class="poster-close" bindtap="onClosePosterModal">✕</view>
      </view>
      
      <view class="poster-preview">
        <image class="poster-image" src="{{posterUrl}}" mode="aspectFit" wx:if="{{posterUrl}}"></image>
        <view class="poster-loading" wx:else>
          <view class="loading-spinner"></view>
          <text>生成中...</text>
        </view>
      </view>
      
      <view class="poster-actions" wx:if="{{posterUrl}}">
        <button class="btn btn-secondary" bindtap="onSavePoster">保存到相册</button>
        <button class="btn btn-primary" bindtap="onSharePoster">分享海报</button>
      </view>
    </view>
  </view>

  <!-- 好友匹配 -->
  <view class="friend-match-section" wx:if="{{friendMatchVisible}}">
    <view class="friend-match-card">
      <view class="friend-match-title">👫 邀请好友测试</view>
      <view class="friend-match-desc">看看你们的匹配度有多高！</view>
      <button class="btn btn-primary btn-small" bindtap="onInviteFriend">邀请好友</button>
    </view>
  </view>

  <!-- 相关测试推荐 -->
  <view class="recommend-section" wx:if="{{recommendTests.length > 0}}">
    <view class="recommend-title">🔥 你可能还喜欢</view>
    <view class="recommend-list">
      <view class="recommend-item" wx:for="{{recommendTests}}" wx:key="_id" bindtap="onRecommendTap" data-test="{{item}}">
        <image class="recommend-cover" src="{{item.coverImage}}" mode="aspectFill"></image>
        <view class="recommend-info">
          <view class="recommend-name">{{item.title}}</view>
          <view class="recommend-count">{{item.completionCount}}人测过</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>{{loadingText}}</text>
  </view>
</view>
