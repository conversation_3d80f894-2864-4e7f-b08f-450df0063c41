// 计算测试结果云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { testId, answers } = event
    const { OPENID } = cloud.getWXContext()

    if (!testId || !answers || !Array.isArray(answers)) {
      return {
        success: false,
        message: '参数错误'
      }
    }

    // 获取测试信息
    const testResult = await db.collection('tests').doc(testId).get()
    if (!testResult.data) {
      return {
        success: false,
        message: '测试不存在'
      }
    }

    // 获取题目和选项信息
    const questionsResult = await db.collection('questions')
      .where({
        testId: testId
      })
      .get()

    const questions = questionsResult.data

    // 计算得分
    const scores = {}
    let totalQuestions = 0

    answers.forEach(answer => {
      const question = questions.find(q => q._id === answer.questionId)
      if (question) {
        totalQuestions++
        const option = question.options.find(opt => opt.id === answer.answer)
        if (option && option.score) {
          Object.keys(option.score).forEach(key => {
            scores[key] = (scores[key] || 0) + option.score[key]
          })
        }
      }
    })

    // 找出得分最高的结果类型
    let maxScore = 0
    let resultType = ''
    Object.keys(scores).forEach(key => {
      if (scores[key] > maxScore) {
        maxScore = scores[key]
        resultType = key
      }
    })

    // 获取结果详情
    const resultDetailResult = await db.collection('test_results')
      .where({
        testId: testId,
        resultType: resultType
      })
      .get()

    if (resultDetailResult.data.length === 0) {
      return {
        success: false,
        message: '结果数据不存在'
      }
    }

    const resultDetail = resultDetailResult.data[0]

    // 计算百分比（相对于最高可能得分）
    const maxPossibleScore = totalQuestions * 3 // 假设每题最高3分
    const percentage = Math.round((maxScore / maxPossibleScore) * 100)

    // 保存用户测试记录
    const userRecord = {
      userId: OPENID,
      testId: testId,
      answers: answers.map(answer => ({
        ...answer,
        timestamp: new Date()
      })),
      result: {
        type: resultType,
        title: resultDetail.title,
        score: scores,
        percentage: percentage
      },
      completedAt: new Date(),
      duration: 0, // 前端计算后传入
      isShared: false,
      shareCount: 0
    }

    await db.collection('user_test_records').add({
      data: userRecord
    })

    // 更新测试完成数
    await db.collection('tests').doc(testId).update({
      data: {
        completionCount: db.command.inc(1)
      }
    })

    // 更新用户统计
    const userResult = await db.collection('users').where({
      openId: OPENID
    }).get()

    if (userResult.data.length > 0) {
      await db.collection('users').doc(userResult.data[0]._id).update({
        data: {
          completedTests: db.command.inc(1),
          totalPoints: db.command.inc(10) // 完成测试获得10积分
        }
      })
    }

    return {
      success: true,
      data: {
        result: {
          type: resultType,
          title: resultDetail.title,
          description: resultDetail.description,
          traits: resultDetail.traits,
          percentage: percentage,
          image: resultDetail.image,
          shareTitle: resultDetail.shareTitle,
          shareImage: resultDetail.shareImage,
          advice: resultDetail.advice,
          compatibility: resultDetail.compatibility
        },
        scores: scores,
        recordId: userRecord._id
      }
    }

  } catch (error) {
    console.error('计算测试结果失败:', error)
    return {
      success: false,
      message: '计算测试结果失败',
      error: error.message
    }
  }
}
