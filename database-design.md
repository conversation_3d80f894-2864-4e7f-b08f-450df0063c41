# 数据库设计文档

## 1. 测试表 (tests)
```json
{
  "_id": "test_001",
  "title": "测测你是哪种奇幻生物？",
  "description": "通过12道有趣的题目，发现你内心深处的奇幻生物属性！",
  "type": "fantasy_creature",
  "category": "personality",
  "difficulty": "easy",
  "questionCount": 12,
  "estimatedTime": 3,
  "coverImage": "https://xxx.com/cover.jpg",
  "tags": ["奇幻", "性格", "有趣"],
  "isHot": true,
  "isRecommended": false,
  "status": "published",
  "viewCount": 15420,
  "shareCount": 3240,
  "likeCount": 8960,
  "completionCount": 12340,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "author": "admin",
  "shareConfig": {
    "title": "我测出来是独角兽！你是什么奇幻生物？",
    "description": "超准的奇幻生物测试，快来发现你的神秘属性！"
  }
}
```

## 2. 题目表 (questions)
```json
{
  "_id": "question_001",
  "testId": "test_001",
  "questionNumber": 1,
  "title": "在一个神秘的森林里，你会选择哪条路？",
  "type": "single_choice",
  "options": [
    {
      "id": "A",
      "text": "阳光洒满的宽阔大道",
      "image": "",
      "score": {"unicorn": 3, "dragon": 1, "phoenix": 2, "wolf": 1}
    },
    {
      "id": "B", 
      "text": "月光下的神秘小径",
      "image": "",
      "score": {"unicorn": 1, "dragon": 2, "phoenix": 1, "wolf": 3}
    },
    {
      "id": "C",
      "text": "充满挑战的崎岖山路",
      "image": "",
      "score": {"unicorn": 1, "dragon": 3, "phoenix": 3, "wolf": 2}
    },
    {
      "id": "D",
      "text": "安静祥和的溪边小路",
      "image": "",
      "score": {"unicorn": 2, "dragon": 1, "phoenix": 1, "wolf": 1}
    }
  ],
  "explanation": "不同的路径选择反映了你的冒险精神和性格倾向",
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

## 3. 测试结果表 (test_results)
```json
{
  "_id": "result_001",
  "testId": "test_001",
  "resultType": "unicorn",
  "title": "纯洁独角兽",
  "description": "你拥有纯洁善良的心灵，总是相信世界的美好。你的存在就像一道光，温暖着身边的每一个人。独角兽代表着希望、治愈和奇迹，这正是你给别人的感觉！",
  "traits": ["善良", "纯真", "治愈", "希望"],
  "percentage": 23.5,
  "image": "https://xxx.com/unicorn.jpg",
  "shareTitle": "我测出来是纯洁独角兽！你是什么奇幻生物？",
  "shareImage": "https://xxx.com/unicorn-share.jpg",
  "advice": "保持你的纯真和善良，这是你最珍贵的品质。",
  "compatibility": {
    "best": ["phoenix", "fairy"],
    "good": ["unicorn", "elf"],
    "normal": ["dragon", "wolf"]
  },
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

## 4. 用户表 (users)
```json
{
  "_id": "user_001",
  "openId": "xxx",
  "unionId": "xxx",
  "nickName": "小明",
  "avatarUrl": "https://xxx.com/avatar.jpg",
  "gender": 1,
  "city": "北京",
  "province": "北京",
  "country": "中国",
  "totalPoints": 156,
  "level": 3,
  "completedTests": 12,
  "sharedCount": 8,
  "invitedFriends": 3,
  "consecutiveCheckIn": 5,
  "lastCheckInDate": "2024-01-01",
  "achievements": ["新手上路", "分享达人"],
  "preferences": {
    "favoriteTypes": ["personality", "mbti"],
    "difficulty": "medium"
  },
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

## 5. 用户测试记录表 (user_test_records)
```json
{
  "_id": "record_001",
  "userId": "user_001",
  "testId": "test_001",
  "answers": [
    {"questionId": "question_001", "answer": "A", "timestamp": "2024-01-01T00:00:00.000Z"},
    {"questionId": "question_002", "answer": "C", "timestamp": "2024-01-01T00:01:00.000Z"}
  ],
  "result": {
    "type": "unicorn",
    "title": "纯洁独角兽",
    "score": {"unicorn": 28, "dragon": 15, "phoenix": 18, "wolf": 12},
    "percentage": 23.5
  },
  "completedAt": "2024-01-01T00:05:00.000Z",
  "duration": 180,
  "isShared": true,
  "shareCount": 2,
  "posterUrl": "https://xxx.com/poster.jpg"
}
```

## 6. 脑筋急转弯表 (brain_teasers)
```json
{
  "_id": "brain_001",
  "question": "什么东西越洗越脏？",
  "answer": "水",
  "explanation": "水用来洗东西，洗的东西越多，水就越脏",
  "difficulty": "easy",
  "category": "logic",
  "hints": ["日常用品", "清洁相关"],
  "tags": ["逻辑", "日常"],
  "viewCount": 1520,
  "correctCount": 890,
  "wrongCount": 630,
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

## 7. 用户脑筋急转弯记录表 (user_brain_records)
```json
{
  "_id": "brain_record_001",
  "userId": "user_001",
  "questionId": "brain_001",
  "userAnswer": "水",
  "isCorrect": true,
  "hintsUsed": 0,
  "timeSpent": 45,
  "pointsEarned": 3,
  "answeredAt": "2024-01-01T00:00:00.000Z"
}
```

## 8. 分享记录表 (share_records)
```json
{
  "_id": "share_001",
  "userId": "user_001",
  "testId": "test_001",
  "shareType": "moments",
  "shareTitle": "我测出来是纯洁独角兽！",
  "shareImage": "https://xxx.com/share.jpg",
  "clickCount": 15,
  "conversionCount": 8,
  "sharedAt": "2024-01-01T00:00:00.000Z"
}
```

## 9. 好友关系表 (friend_relations)
```json
{
  "_id": "friend_001",
  "userId": "user_001",
  "friendId": "user_002",
  "relationshipType": "invited",
  "testId": "test_001",
  "matchScore": 85.5,
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

## 10. 系统配置表 (system_configs)
```json
{
  "_id": "config_001",
  "key": "daily_check_in_points",
  "value": 2,
  "description": "每日签到获得积分",
  "type": "number",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```
