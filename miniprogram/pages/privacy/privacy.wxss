/* privacy.wxss */

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
}

.content {
  padding: 30rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.highlight {
  color: #667eea;
  font-weight: 500;
}

.footer {
  text-align: center;
  padding: 40rpx 0;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}
