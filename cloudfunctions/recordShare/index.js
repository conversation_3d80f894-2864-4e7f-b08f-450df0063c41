// 记录分享行为云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { testId, shareType } = event
    const { OPENID } = cloud.getWXContext()

    if (!testId || !shareType) {
      return {
        success: false,
        message: '参数不完整'
      }
    }

    // 记录分享行为
    const shareRecord = {
      userId: OPENID,
      testId: testId,
      shareType: shareType, // 'moments', 'friend', 'timeline'
      shareTitle: event.shareTitle || '',
      shareImage: event.shareImage || '',
      clickCount: 0,
      conversionCount: 0,
      sharedAt: new Date()
    }

    await db.collection('share_records').add({
      data: shareRecord
    })

    // 更新测试分享数
    await db.collection('tests').doc(testId).update({
      data: {
        shareCount: db.command.inc(1)
      }
    })

    // 更新用户分享统计
    const userResult = await db.collection('users').where({
      openId: OPENID
    }).get()

    if (userResult.data.length > 0) {
      await db.collection('users').doc(userResult.data[0]._id).update({
        data: {
          sharedCount: db.command.inc(1),
          totalPoints: db.command.inc(5) // 分享获得5积分
        }
      })
    }

    return {
      success: true,
      data: {
        pointsEarned: 5,
        message: '分享成功，获得5积分！'
      }
    }

  } catch (error) {
    console.error('记录分享失败:', error)
    return {
      success: false,
      message: '记录分享失败',
      error: error.message
    }
  }
}
