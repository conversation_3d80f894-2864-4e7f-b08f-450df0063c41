// API 请求封装
const config = require('./config.js')
const util = require('./util.js')

class API {
  constructor() {
    this.baseUrl = ''
    this.timeout = config.api.timeout
    this.retryTimes = config.api.retryTimes
  }

  /**
   * 通用请求方法
   */
  request(options) {
    return new Promise((resolve, reject) => {
      const { url, method = 'GET', data = {}, header = {}, showLoading = true, retryCount = 0 } = options

      if (showLoading) {
        util.showLoading()
      }

      wx.cloud.callFunction({
        name: url,
        data,
        success: (res) => {
          if (showLoading) {
            util.hideLoading()
          }
          
          if (res.result && res.result.success) {
            resolve(res.result.data)
          } else {
            const errorMsg = res.result?.message || '请求失败'
            util.showError(errorMsg)
            reject(new Error(errorMsg))
          }
        },
        fail: (error) => {
          if (showLoading) {
            util.hideLoading()
          }
          
          // 重试机制
          if (retryCount < this.retryTimes) {
            console.log(`请求失败，正在重试第${retryCount + 1}次...`)
            setTimeout(() => {
              this.request({
                ...options,
                retryCount: retryCount + 1
              }).then(resolve).catch(reject)
            }, 1000 * (retryCount + 1))
          } else {
            util.showError('网络请求失败，请检查网络连接')
            reject(error)
          }
        }
      })
    })
  }

  /**
   * 获取测试列表
   */
  getTestList(params = {}) {
    return this.request({
      url: 'getTestList',
      data: params
    })
  }

  /**
   * 获取测试详情
   */
  getTestDetail(testId) {
    return this.request({
      url: 'getTestDetail',
      data: { testId }
    })
  }

  /**
   * 获取测试题目
   */
  getTestQuestions(testId) {
    return this.request({
      url: 'getTestQuestions',
      data: { testId }
    })
  }

  /**
   * 提交测试答案
   */
  submitTestAnswers(testId, answers) {
    return this.request({
      url: 'submitTestAnswers',
      data: { testId, answers }
    })
  }

  /**
   * 获取测试结果
   */
  getTestResult(testId, answers) {
    return this.request({
      url: 'getTestResult',
      data: { testId, answers }
    })
  }

  /**
   * 生成测试海报
   */
  generatePoster(resultData) {
    return this.request({
      url: 'generatePoster',
      data: resultData
    })
  }

  /**
   * 记录分享行为
   */
  recordShare(testId, shareType) {
    return this.request({
      url: 'recordShare',
      data: { testId, shareType },
      showLoading: false
    })
  }

  /**
   * 获取脑筋急转弯题目
   */
  getBrainTeaserQuestion() {
    return this.request({
      url: 'getBrainTeaserQuestion'
    })
  }

  /**
   * 提交脑筋急转弯答案
   */
  submitBrainTeaserAnswer(questionId, answer) {
    return this.request({
      url: 'submitBrainTeaserAnswer',
      data: { questionId, answer }
    })
  }

  /**
   * 获取排行榜
   */
  getRanking(type = 'total', page = 1, pageSize = 20) {
    return this.request({
      url: 'getRanking',
      data: { type, page, pageSize }
    })
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.request({
      url: 'getUserInfo',
      showLoading: false
    })
  }

  /**
   * 更新用户信息
   */
  updateUserInfo(userInfo) {
    return this.request({
      url: 'updateUserInfo',
      data: userInfo,
      showLoading: false
    })
  }

  /**
   * 签到
   */
  dailyCheckIn() {
    return this.request({
      url: 'dailyCheckIn'
    })
  }

  /**
   * 获取好友匹配度
   */
  getFriendMatch(friendOpenId, testId) {
    return this.request({
      url: 'getFriendMatch',
      data: { friendOpenId, testId }
    })
  }

  /**
   * 邀请好友
   */
  inviteFriend(friendOpenId, testId) {
    return this.request({
      url: 'inviteFriend',
      data: { friendOpenId, testId }
    })
  }

  /**
   * 获取热门测试
   */
  getHotTests(limit = 10) {
    return this.request({
      url: 'getHotTests',
      data: { limit }
    })
  }

  /**
   * 搜索测试
   */
  searchTests(keyword, page = 1, pageSize = 20) {
    return this.request({
      url: 'searchTests',
      data: { keyword, page, pageSize }
    })
  }

  /**
   * 获取用户测试历史
   */
  getUserTestHistory(page = 1, pageSize = 20) {
    return this.request({
      url: 'getUserTestHistory',
      data: { page, pageSize }
    })
  }

  /**
   * 点赞测试
   */
  likeTest(testId) {
    return this.request({
      url: 'likeTest',
      data: { testId },
      showLoading: false
    })
  }

  /**
   * 收藏测试
   */
  favoriteTest(testId) {
    return this.request({
      url: 'favoriteTest',
      data: { testId },
      showLoading: false
    })
  }

  /**
   * 举报测试
   */
  reportTest(testId, reason) {
    return this.request({
      url: 'reportTest',
      data: { testId, reason }
    })
  }
}

// 创建API实例
const api = new API()

module.exports = api
