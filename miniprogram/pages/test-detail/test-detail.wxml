<!--test-detail.wxml-->
<view class="container">
  <!-- 测试封面 -->
  <view class="cover-section">
    <image class="cover-image" src="{{testInfo.coverImage}}" mode="aspectFill"></image>
    <view class="cover-overlay">
      <view class="cover-content">
        <view class="test-title">{{testInfo.title}}</view>
        <view class="test-desc">{{testInfo.description}}</view>
        <view class="test-stats">
          <view class="stat-item">
            <text class="stat-icon">👥</text>
            <text class="stat-text">{{testInfo.completionCount}}人测过</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">⏱</text>
            <text class="stat-text">约{{testInfo.estimatedTime}}分钟</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">📝</text>
            <text class="stat-text">{{testInfo.questionCount}}道题</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试信息 -->
  <view class="info-section">
    <view class="info-card">
      <view class="info-header">
        <view class="info-title">测试介绍</view>
        <view class="difficulty-badge difficulty-{{testInfo.difficulty}}">
          {{testInfo.difficulty === 'easy' ? '简单' : testInfo.difficulty === 'medium' ? '中等' : '困难'}}
        </view>
      </view>
      <view class="info-content">
        <text class="info-text">{{testInfo.description}}</text>
      </view>
      <view class="info-tags">
        <text class="tag" wx:for="{{testInfo.tags}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 测试说明 -->
  <view class="instruction-section">
    <view class="instruction-card">
      <view class="instruction-title">📋 测试说明</view>
      <view class="instruction-list">
        <view class="instruction-item">
          <text class="instruction-icon">1️⃣</text>
          <text class="instruction-text">请根据真实感受选择答案</text>
        </view>
        <view class="instruction-item">
          <text class="instruction-icon">2️⃣</text>
          <text class="instruction-text">每道题都要认真作答</text>
        </view>
        <view class="instruction-item">
          <text class="instruction-icon">3️⃣</text>
          <text class="instruction-text">测试结果仅供娱乐参考</text>
        </view>
        <view class="instruction-item">
          <text class="instruction-icon">4️⃣</text>
          <text class="instruction-text">完成后可生成专属海报分享</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section" wx:if="{{userRecord}}">
    <view class="history-card">
      <view class="history-header">
        <view class="history-title">🏆 你的测试记录</view>
        <view class="history-date">{{userRecord.completedAt}}</view>
      </view>
      <view class="history-result">
        <view class="result-type">{{userRecord.result.title}}</view>
        <view class="result-desc">点击查看详细结果</view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="btn btn-secondary btn-large" bindtap="onShareTest">
        <text class="btn-icon">📤</text>
        <text>分享测试</text>
      </button>
      <button class="btn btn-primary btn-large" bindtap="onStartTest">
        <text class="btn-icon">🚀</text>
        <text>{{userRecord ? '重新测试' : '开始测试'}}</text>
      </button>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="recommend-section" wx:if="{{recommendTests.length > 0}}">
    <view class="recommend-title">🔥 相关推荐</view>
    <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recommend-item" wx:for="{{recommendTests}}" wx:key="_id" bindtap="onRecommendTap" data-test="{{item}}">
        <image class="recommend-cover" src="{{item.coverImage}}" mode="aspectFill"></image>
        <view class="recommend-info">
          <view class="recommend-name">{{item.title}}</view>
          <view class="recommend-count">{{item.completionCount}}人测过</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</view>
