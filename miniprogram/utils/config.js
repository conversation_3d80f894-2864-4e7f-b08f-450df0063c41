// 全局配置文件
const config = {
  // 云开发环境ID
  cloudEnvId: 'your-cloud-env-id',
  
  // 测试类型配置
  testTypes: {
    PERSONALITY: 'personality',
    MBTI: 'mbti',
    FANTASY_CREATURE: 'fantasy_creature',
    CAREER: 'career',
    NOVEL_CHARACTER: 'novel_character',
    LOVE_MATCH: 'love_match'
  },
  
  // 测试难度配置
  testDifficulty: {
    EASY: 'easy',
    MEDIUM: 'medium',
    HARD: 'hard'
  },
  
  // 分享配置
  share: {
    title: '我刚完成了一个超准的测试，你也来试试吧！',
    path: '/pages/index/index',
    imageUrl: '/images/share-default.jpg'
  },
  
  // 海报配置
  poster: {
    width: 750,
    height: 1334,
    backgroundColor: '#ffffff',
    templates: {
      default: 'default',
      gradient: 'gradient',
      minimal: 'minimal',
      colorful: 'colorful'
    }
  },
  
  // 积分配置
  points: {
    completeTest: 10,
    shareTest: 5,
    inviteFriend: 20,
    dailyLogin: 2,
    brainTeaserCorrect: 3,
    brainTeaserWrong: 1
  },
  
  // 排行榜配置
  ranking: {
    pageSize: 20,
    refreshInterval: 30000 // 30秒
  },
  
  // 缓存配置
  cache: {
    testData: 'test_data_cache',
    userInfo: 'user_info_cache',
    ranking: 'ranking_cache',
    expireTime: 24 * 60 * 60 * 1000 // 24小时
  },
  
  // API配置
  api: {
    timeout: 10000,
    retryTimes: 3
  },
  
  // 版本信息
  version: '1.0.0',
  
  // 功能开关
  features: {
    brainTeaser: true,
    socialShare: true,
    ranking: true,
    poster: true,
    ugc: false // 用户投稿功能，暂时关闭
  }
}

module.exports = config
