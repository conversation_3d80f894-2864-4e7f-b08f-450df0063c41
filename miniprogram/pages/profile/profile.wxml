<!--profile.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-section">
    <view class="user-card">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" bindtap="onAvatarTap"></image>
      <view class="user-info">
        <view class="user-name">{{userInfo.nickName || '点击登录'}}</view>
        <view class="user-level">Lv.{{userInfo.level || 1}} · {{userInfo.totalPoints || 0}}积分</view>
      </view>
      <view class="user-badge">{{userInfo.achievements && userInfo.achievements.length || 0}}个成就</view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stats-item">
        <view class="stats-number">{{userInfo.completedTests || 0}}</view>
        <view class="stats-label">完成测试</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{userInfo.sharedCount || 0}}</view>
        <view class="stats-label">分享次数</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{userInfo.invitedFriends || 0}}</view>
        <view class="stats-label">邀请好友</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{userInfo.consecutiveCheckIn || 0}}</view>
        <view class="stats-label">连续签到</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="onMenuTap" data-type="history">
        <view class="menu-icon">📋</view>
        <view class="menu-text">测试历史</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onMenuTap" data-type="favorites">
        <view class="menu-icon">❤️</view>
        <view class="menu-text">我的收藏</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onMenuTap" data-type="achievements">
        <view class="menu-icon">🏆</view>
        <view class="menu-text">成就徽章</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="onMenuTap" data-type="checkin">
        <view class="menu-icon">📅</view>
        <view class="menu-text">每日签到</view>
        <view class="menu-badge" wx:if="{{!todayChecked}}">!</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onMenuTap" data-type="invite">
        <view class="menu-icon">👥</view>
        <view class="menu-text">邀请好友</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="onMenuTap" data-type="feedback">
        <view class="menu-icon">💬</view>
        <view class="menu-text">意见反馈</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onMenuTap" data-type="about">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onMenuTap" data-type="privacy">
        <view class="menu-icon">🔒</view>
        <view class="menu-text">隐私政策</view>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</view>
