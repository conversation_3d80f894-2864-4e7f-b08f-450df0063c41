/* test-question.wxss */

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 进度条区域 */
.progress-section {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 题目区域 */
.question-section {
  padding: 30rpx;
}

.question-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.question-number {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.question-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.question-image {
  width: 100%;
  max-height: 400rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

/* 选项列表 */
.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.selected {
  background: #e8f4fd;
  border-color: #667eea;
}

.option-label {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.option-item.selected .option-label {
  background: #4facfe;
}

.option-content {
  flex: 1;
}

.option-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
}

.option-image {
  width: 100%;
  max-height: 200rpx;
  border-radius: 8rpx;
  margin-top: 15rpx;
}

.option-check {
  font-size: 32rpx;
  color: #667eea;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 操作按钮 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-buttons .btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 题目导航 */
.navigation-section {
  padding: 30rpx;
  background: white;
  margin: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.nav-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.nav-dots {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.nav-dot {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-dot.completed {
  background: #e8f4fd;
  color: #667eea;
}

.nav-dot.current {
  background: #667eea;
  color: white;
  transform: scale(1.1);
}

/* 退出确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.modal-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.modal-buttons {
  display: flex;
  gap: 20rpx;
}

.modal-buttons .btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}
