// profile.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: false,
    userInfo: {},
    todayChecked: false
  },

  onLoad() {
    this.loadUserInfo()
  },

  onShow() {
    this.loadUserInfo()
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      this.setData({ loading: true })

      // 模拟用户数据
      const mockUserInfo = {
        nickName: '测试用户',
        avatarUrl: '/images/default-avatar.png',
        level: 3,
        totalPoints: 450,
        completedTests: 12,
        sharedCount: 8,
        invitedFriends: 3,
        consecutiveCheckIn: 5,
        achievements: ['新手上路', '分享达人']
      }

      this.setData({
        userInfo: mockUserInfo,
        todayChecked: false // 今天是否已签到
      })

    } catch (error) {
      console.error('加载用户信息失败:', error)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 头像点击 - 获取用户信息
   */
  onAvatarTap() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            ...res.userInfo
          }
        })
        // 这里可以调用API更新用户信息
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
      }
    })
  },

  /**
   * 菜单点击
   */
  onMenuTap(e) {
    const type = e.currentTarget.dataset.type

    switch (type) {
      case 'history':
        wx.navigateTo({
          url: '/pages/test-history/test-history'
        })
        break
      case 'favorites':
        wx.navigateTo({
          url: '/pages/favorites/favorites'
        })
        break
      case 'achievements':
        wx.navigateTo({
          url: '/pages/achievements/achievements'
        })
        break
      case 'checkin':
        this.onCheckIn()
        break
      case 'invite':
        this.onInviteFriend()
        break
      case 'feedback':
        wx.navigateTo({
          url: '/pages/feedback/feedback'
        })
        break
      case 'about':
        wx.navigateTo({
          url: '/pages/about/about'
        })
        break
      case 'privacy':
        wx.navigateTo({
          url: '/pages/privacy/privacy'
        })
        break
      default:
        util.showError('功能开发中...')
    }
  },

  /**
   * 签到
   */
  async onCheckIn() {
    if (this.data.todayChecked) {
      util.showError('今天已经签到过了')
      return
    }

    try {
      // const result = await api.dailyCheckIn()
      // 模拟签到成功
      const result = { pointsEarned: 2 }
      
      this.setData({ todayChecked: true })
      util.showSuccess(`签到成功！获得${result.pointsEarned}积分`)
      
      // 更新用户信息
      this.loadUserInfo()
      
    } catch (error) {
      console.error('签到失败:', error)
      util.showError('签到失败，请重试')
    }
  },

  /**
   * 邀请好友
   */
  onInviteFriend() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage']
    })
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    return {
      title: '发现了一个超有趣的测试小程序，快来一起玩吧！',
      path: '/pages/index/index',
      imageUrl: '/images/share-invite.jpg'
    }
  }
})
