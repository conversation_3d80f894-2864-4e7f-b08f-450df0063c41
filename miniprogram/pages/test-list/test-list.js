// test-list.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: false,
    testList: [],
    currentType: '',
    sortBy: 'hot',
    searchKeyword: '',
    page: 1,
    pageSize: 10,
    hasMore: true,
    total: 0
  },

  onLoad(options) {
    // 从参数中获取筛选类型
    if (options.type) {
      this.setData({
        currentType: options.type
      })
    }
    
    this.loadTestList(true)
  },

  onShow() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: this.getPageTitle()
    })
  },

  onPullDownRefresh() {
    this.loadTestList(true).then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.onLoadMore()
    }
  },

  /**
   * 获取页面标题
   */
  getPageTitle() {
    const typeMap = {
      '': '全部测试',
      'personality': '性格测试',
      'mbti': 'MBTI测试',
      'fantasy_creature': '奇幻生物测试',
      'career': '职场测试',
      'love_match': '恋爱匹配测试'
    }
    return typeMap[this.data.currentType] || '测试列表'
  },

  /**
   * 加载测试列表
   */
  async loadTestList(refresh = false) {
    if (this.data.loading) return

    try {
      this.setData({ loading: true })

      const page = refresh ? 1 : this.data.page
      const params = {
        type: this.data.currentType,
        sortBy: this.data.sortBy,
        keyword: this.data.searchKeyword,
        page: page,
        pageSize: this.data.pageSize
      }

      const result = await api.getTestList(params)

      const newList = refresh ? result.list : [...this.data.testList, ...result.list]

      this.setData({
        testList: newList,
        page: page,
        total: result.total,
        hasMore: result.hasMore
      })

      if (!refresh) {
        this.setData({
          page: page + 1
        })
      }

    } catch (error) {
      console.error('加载测试列表失败:', error)
      util.showError('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    
    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.loadTestList(true)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    this.loadTestList(true)
  },

  /**
   * 清除搜索
   */
  onClearSearch() {
    this.setData({
      searchKeyword: ''
    })
    this.loadTestList(true)
  },

  /**
   * 分类筛选
   */
  onFilterTap(e) {
    const type = e.currentTarget.dataset.type
    if (type === this.data.currentType) return

    this.setData({
      currentType: type,
      page: 1
    })
    
    // 更新页面标题
    wx.setNavigationBarTitle({
      title: this.getPageTitle()
    })
    
    this.loadTestList(true)
  },

  /**
   * 排序切换
   */
  onSortTap(e) {
    const sort = e.currentTarget.dataset.sort
    if (sort === this.data.sortBy) return

    this.setData({
      sortBy: sort,
      page: 1
    })
    
    this.loadTestList(true)
  },

  /**
   * 测试卡片点击
   */
  onTestTap(e) {
    const test = e.currentTarget.dataset.test
    wx.navigateTo({
      url: `/pages/test-detail/test-detail?id=${test._id}`
    })
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadTestList(false)
    }
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    const typeMap = {
      'personality': '性格测试',
      'mbti': 'MBTI测试',
      'fantasy_creature': '奇幻生物测试',
      'career': '职场测试',
      'love_match': '恋爱匹配测试'
    }
    
    const typeName = typeMap[this.data.currentType] || '趣味测试'
    
    return {
      title: `发现了很多有趣的${typeName}，快来一起玩吧！`,
      path: `/pages/test-list/test-list?type=${this.data.currentType}`,
      imageUrl: '/images/share-test-list.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '趣味测试 - 发现真实的自己',
      imageUrl: '/images/share-timeline.jpg'
    }
  }
})
