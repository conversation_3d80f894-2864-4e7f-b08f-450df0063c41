// test-result.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: false,
    loadingText: '',
    testId: '',
    resultData: null,
    duration: 0,
    showScores: false,
    scoresList: [],
    showPosterModal: false,
    posterUrl: '',
    friendMatchVisible: true,
    recommendTests: []
  },

  onLoad(options) {
    if (options.testId && options.resultData) {
      this.setData({
        testId: options.testId,
        resultData: JSON.parse(decodeURIComponent(options.resultData)),
        duration: parseInt(options.duration) || 0
      })
      
      this.initPage()
    } else if (options.recordId) {
      // 从历史记录查看结果
      this.loadResultFromRecord(options.recordId)
    } else {
      util.showError('参数错误')
      wx.navigateBack()
    }
  },

  onShow() {
    // 设置页面标题
    if (this.data.resultData) {
      wx.setNavigationBarTitle({
        title: `你是${this.data.resultData.result.title}`
      })
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    this.processScoresData()
    await this.loadRecommendTests()
    
    // 延迟显示庆祝动画
    setTimeout(() => {
      this.showCelebration()
    }, 500)
  },

  /**
   * 处理分数数据
   */
  processScoresData() {
    if (!this.data.resultData.scores) return

    const scores = this.data.resultData.scores
    const maxScore = Math.max(...Object.values(scores))
    
    const scoresList = Object.keys(scores).map(key => {
      const score = scores[key]
      return {
        type: key,
        label: this.getScoreLabel(key),
        score: score,
        percentage: maxScore > 0 ? (score / maxScore) * 100 : 0
      }
    }).sort((a, b) => b.score - a.score)

    this.setData({ scoresList })
  },

  /**
   * 获取分数标签
   */
  getScoreLabel(type) {
    const labelMap = {
      unicorn: '独角兽',
      dragon: '龙',
      phoenix: '凤凰',
      wolf: '狼',
      // 可以根据测试类型扩展更多标签
    }
    return labelMap[type] || type
  },

  /**
   * 显示庆祝动画
   */
  showCelebration() {
    // 这里可以添加庆祝动画逻辑
    console.log('显示庆祝动画')
  },

  /**
   * 从记录加载结果
   */
  async loadResultFromRecord(recordId) {
    try {
      this.setData({ loading: true, loadingText: '加载结果中...' })
      
      // 这里应该调用API获取历史记录
      // const record = await api.getTestRecord(recordId)
      // this.setData({ resultData: record, testId: record.testId })
      
    } catch (error) {
      console.error('加载历史记录失败:', error)
      util.showError('加载失败')
      wx.navigateBack()
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载推荐测试
   */
  async loadRecommendTests() {
    try {
      const result = await api.getTestList({
        pageSize: 4,
        sortBy: 'hot'
      })
      
      // 排除当前测试
      const recommendTests = (result.list || []).filter(test => test._id !== this.data.testId)
      this.setData({ recommendTests: recommendTests.slice(0, 3) })
      
    } catch (error) {
      console.error('加载推荐测试失败:', error)
    }
  },

  /**
   * 切换分数显示
   */
  onToggleScores() {
    this.setData({
      showScores: !this.data.showScores
    })
  },

  /**
   * 重新测试
   */
  onRetakeTest() {
    util.showConfirm('确定要重新测试吗？').then(confirmed => {
      if (confirmed) {
        wx.redirectTo({
          url: `/pages/test-question/test-question?id=${this.data.testId}`
        })
      }
    })
  },

  /**
   * 生成海报
   */
  async onGeneratePoster() {
    try {
      this.setData({ 
        showPosterModal: true,
        posterUrl: ''
      })

      // 获取用户信息
      const userInfo = await this.getUserInfo()
      
      const posterData = {
        testResult: {
          ...this.data.resultData.result,
          testId: this.data.testId,
          testTitle: '趣味测试'
        },
        userInfo: userInfo,
        template: 'default'
      }

      const result = await api.generatePoster(posterData)
      
      this.setData({
        posterUrl: result.posterUrl
      })

    } catch (error) {
      console.error('生成海报失败:', error)
      util.showError('生成海报失败，请重试')
      this.setData({ showPosterModal: false })
    }
  },

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    return new Promise((resolve) => {
      wx.getUserProfile({
        desc: '用于生成个性化测试海报',
        success: (res) => {
          resolve(res.userInfo)
        },
        fail: () => {
          // 使用默认信息
          resolve({
            nickName: '测试用户',
            avatarUrl: '/images/default-avatar.png'
          })
        }
      })
    })
  },

  /**
   * 关闭海报弹窗
   */
  onClosePosterModal() {
    this.setData({ showPosterModal: false })
  },

  /**
   * 保存海报
   */
  async onSavePoster() {
    try {
      // 下载图片到本地
      const downloadResult = await wx.downloadFile({
        url: this.data.posterUrl
      })

      // 保存到相册
      await util.saveImageToPhotosAlbum(downloadResult.tempFilePath)
      util.showSuccess('保存成功')
      
    } catch (error) {
      console.error('保存海报失败:', error)
      if (error.errMsg && error.errMsg.includes('auth deny')) {
        util.showError('请允许访问相册权限')
      } else {
        util.showError('保存失败，请重试')
      }
    }
  },

  /**
   * 分享海报
   */
  onSharePoster() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 分享结果
   */
  onShareResult() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 邀请好友
   */
  onInviteFriend() {
    // 触发分享邀请
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage']
    })
  },

  /**
   * 推荐测试点击
   */
  onRecommendTap(e) {
    const test = e.currentTarget.dataset.test
    wx.navigateTo({
      url: `/pages/test-detail/test-detail?id=${test._id}`
    })
  },

  /**
   * 返回首页
   */
  onBackToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    const result = this.data.resultData.result
    return {
      title: `我测出来是${result.title}！你也来试试吧`,
      path: `/pages/test-detail/test-detail?id=${this.data.testId}`,
      imageUrl: this.data.posterUrl || result.shareImage || '/images/share-default.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const result = this.data.resultData.result
    return {
      title: `我是${result.title} - 趣味测试`,
      imageUrl: this.data.posterUrl || result.shareImage || '/images/share-timeline.jpg'
    }
  }
})
