/* test-list.wxss */

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 搜索栏 */
.search-section {
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-clear {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
}

/* 分类筛选 */
.filter-section {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
  padding: 20rpx 0;
}

.filter-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin: 0 15rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #666;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.filter-item:first-child {
  margin-left: 30rpx;
}

.filter-item:last-child {
  margin-right: 30rpx;
}

/* 排序选项 */
.sort-section {
  display: flex;
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 40rpx;
}

.sort-item {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 0;
  position: relative;
  transition: color 0.3s ease;
}

.sort-item.active {
  color: #667eea;
  font-weight: 500;
}

.sort-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #667eea;
  border-radius: 2rpx;
}

/* 测试列表 */
.test-list {
  padding: 20rpx 30rpx;
}

.test-card {
  position: relative;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.test-card:active {
  transform: scale(0.98);
}

.test-cover {
  width: 100%;
  height: 300rpx;
}

.test-info {
  padding: 30rpx;
}

.test-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.test-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.test-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999;
}

.test-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666;
}

.tag.difficulty-easy {
  background: #e8f5e8;
  color: #52c41a;
}

.tag.difficulty-medium {
  background: #fff7e6;
  color: #fa8c16;
}

.tag.difficulty-hard {
  background: #fff2f0;
  color: #f5222d;
}

.test-badges {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 10rpx;
}

.badge {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
}

.badge.hot {
  background: rgba(255, 87, 34, 0.9);
  color: white;
}

.badge.recommend {
  background: rgba(255, 193, 7, 0.9);
  color: white;
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: #f5f5f5;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #e0e0e0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}
