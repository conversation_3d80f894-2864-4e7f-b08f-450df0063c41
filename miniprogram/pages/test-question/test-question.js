// test-question.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: true,
    loadingText: '加载题目中...',
    testId: '',
    testInfo: {},
    questions: [],
    currentQuestionIndex: 0,
    currentQuestion: null,
    selectedAnswer: '',
    answers: [],
    progress: 0,
    isLastQuestion: false,
    showExitModal: false,
    startTime: 0
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        testId: options.id,
        startTime: Date.now()
      })
      this.loadTestQuestions()
    } else {
      util.showError('测试ID不能为空')
      wx.navigateBack()
    }
  },

  onShow() {
    // 禁用返回按钮，使用自定义退出逻辑
    wx.hideHomeButton()
  },

  /**
   * 加载测试题目
   */
  async loadTestQuestions() {
    try {
      this.setData({ 
        loading: true,
        loadingText: '加载题目中...'
      })

      const result = await api.getTestQuestions(this.data.testId)
      
      this.setData({
        testInfo: result.test,
        questions: result.questions,
        currentQuestion: result.questions[0],
        isLastQuestion: result.questions.length === 1
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: result.test.title
      })

      this.updateProgress()

    } catch (error) {
      console.error('加载测试题目失败:', error)
      util.showError('加载失败，请重试')
      wx.navigateBack()
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 选项点击
   */
  onOptionTap(e) {
    const optionId = e.currentTarget.dataset.option
    this.setData({
      selectedAnswer: optionId
    })

    // 保存当前题目答案
    this.saveCurrentAnswer()
  },

  /**
   * 保存当前题目答案
   */
  saveCurrentAnswer() {
    const { currentQuestionIndex, selectedAnswer, currentQuestion } = this.data
    const answers = [...this.data.answers]
    
    // 更新或添加答案
    const existingIndex = answers.findIndex(answer => answer.questionId === currentQuestion._id)
    const answerData = {
      questionId: currentQuestion._id,
      answer: selectedAnswer,
      timestamp: new Date()
    }

    if (existingIndex >= 0) {
      answers[existingIndex] = answerData
    } else {
      answers.push(answerData)
    }

    this.setData({ answers })
  },

  /**
   * 下一题
   */
  onNextQuestion() {
    if (!this.data.selectedAnswer) {
      util.showError('请选择一个答案')
      return
    }

    if (this.data.isLastQuestion) {
      this.submitTest()
    } else {
      this.goToQuestion(this.data.currentQuestionIndex + 1)
    }
  },

  /**
   * 上一题
   */
  onPrevQuestion() {
    if (this.data.currentQuestionIndex > 0) {
      this.goToQuestion(this.data.currentQuestionIndex - 1)
    }
  },

  /**
   * 跳转到指定题目
   */
  goToQuestion(index) {
    if (index < 0 || index >= this.data.questions.length) return

    const question = this.data.questions[index]
    const existingAnswer = this.data.answers.find(answer => answer.questionId === question._id)

    this.setData({
      currentQuestionIndex: index,
      currentQuestion: question,
      selectedAnswer: existingAnswer ? existingAnswer.answer : '',
      isLastQuestion: index === this.data.questions.length - 1
    })

    this.updateProgress()
  },

  /**
   * 题目导航点击
   */
  onNavDotTap(e) {
    const index = e.currentTarget.dataset.index
    this.goToQuestion(index)
  },

  /**
   * 更新进度
   */
  updateProgress() {
    const progress = ((this.data.currentQuestionIndex + 1) / this.data.questions.length) * 100
    this.setData({ progress })
  },

  /**
   * 提交测试
   */
  async submitTest() {
    try {
      this.setData({
        loading: true,
        loadingText: '计算结果中...'
      })

      // 计算测试时长
      const duration = Math.floor((Date.now() - this.data.startTime) / 1000)

      const result = await api.getTestResult(this.data.testId, this.data.answers)

      // 跳转到结果页面
      wx.redirectTo({
        url: `/pages/test-result/test-result?testId=${this.data.testId}&resultData=${encodeURIComponent(JSON.stringify(result))}&duration=${duration}`
      })

    } catch (error) {
      console.error('提交测试失败:', error)
      util.showError('提交失败，请重试')
      this.setData({ loading: false })
    }
  },

  /**
   * 返回按钮处理
   */
  onBackTap() {
    this.setData({ showExitModal: true })
  },

  /**
   * 确认退出
   */
  onConfirmExit() {
    wx.navigateBack()
  },

  /**
   * 取消退出
   */
  onCancelExit() {
    this.setData({ showExitModal: false })
  },

  /**
   * 页面卸载时的处理
   */
  onUnload() {
    // 可以在这里保存草稿等
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    return {
      title: `我正在做${this.data.testInfo.title}，快来一起测试吧！`,
      path: `/pages/test-detail/test-detail?id=${this.data.testId}`,
      imageUrl: this.data.testInfo.coverImage || '/images/share-default.jpg'
    }
  }
})
