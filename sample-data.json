{"tests": [{"_id": "test_001", "title": "测测你是哪种奇幻生物？", "description": "通过12道有趣的题目，发现你内心深处的奇幻生物属性！每个人心中都住着一个神秘的奇幻生物，它代表着你最真实的性格特质。", "type": "fantasy_creature", "category": "personality", "difficulty": "easy", "questionCount": 12, "estimatedTime": 3, "coverImage": "https://example.com/fantasy-cover.jpg", "tags": ["奇幻", "性格", "有趣"], "isHot": true, "isRecommended": true, "status": "published", "viewCount": 15420, "shareCount": 3240, "likeCount": 8960, "completionCount": 12340, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "author": "admin", "shareConfig": {"title": "我测出来是独角兽！你是什么奇幻生物？", "description": "超准的奇幻生物测试，快来发现你的神秘属性！"}}, {"_id": "test_002", "title": "职场人格大揭秘", "description": "了解你在职场中的真实性格，发现你的职场优势和发展方向。", "type": "career", "category": "career", "difficulty": "medium", "questionCount": 15, "estimatedTime": 5, "coverImage": "https://example.com/career-cover.jpg", "tags": ["职场", "性格", "发展"], "isHot": true, "isRecommended": false, "status": "published", "viewCount": 8920, "shareCount": 1840, "likeCount": 5230, "completionCount": 7650, "createdAt": "2024-01-02T00:00:00.000Z", "updatedAt": "2024-01-02T00:00:00.000Z", "author": "admin"}], "questions": [{"_id": "question_001", "testId": "test_001", "questionNumber": 1, "title": "在一个神秘的森林里，你会选择哪条路？", "type": "single_choice", "options": [{"id": "A", "text": "阳光洒满的宽阔大道", "image": "", "score": {"unicorn": 3, "dragon": 1, "phoenix": 2, "wolf": 1}}, {"id": "B", "text": "月光下的神秘小径", "image": "", "score": {"unicorn": 1, "dragon": 2, "phoenix": 1, "wolf": 3}}, {"id": "C", "text": "充满挑战的崎岖山路", "image": "", "score": {"unicorn": 1, "dragon": 3, "phoenix": 3, "wolf": 2}}, {"id": "D", "text": "安静祥和的溪边小路", "image": "", "score": {"unicorn": 2, "dragon": 1, "phoenix": 1, "wolf": 1}}], "explanation": "不同的路径选择反映了你的冒险精神和性格倾向", "createdAt": "2024-01-01T00:00:00.000Z"}, {"_id": "question_002", "testId": "test_001", "questionNumber": 2, "title": "如果你拥有魔法，你最想要什么能力？", "type": "single_choice", "options": [{"id": "A", "text": "治愈他人的痛苦", "image": "", "score": {"unicorn": 3, "dragon": 1, "phoenix": 2, "wolf": 1}}, {"id": "B", "text": "控制火焰的力量", "image": "", "score": {"unicorn": 1, "dragon": 3, "phoenix": 3, "wolf": 1}}, {"id": "C", "text": "与动物沟通", "image": "", "score": {"unicorn": 2, "dragon": 1, "phoenix": 1, "wolf": 3}}, {"id": "D", "text": "预知未来", "image": "", "score": {"unicorn": 1, "dragon": 2, "phoenix": 3, "wolf": 2}}], "explanation": "魔法能力的选择体现了你内心的价值观", "createdAt": "2024-01-01T00:00:00.000Z"}], "test_results": [{"_id": "result_001", "testId": "test_001", "resultType": "unicorn", "title": "纯洁独角兽", "description": "你拥有纯洁善良的心灵，总是相信世界的美好。你的存在就像一道光，温暖着身边的每一个人。独角兽代表着希望、治愈和奇迹，这正是你给别人的感觉！", "traits": ["善良", "纯真", "治愈", "希望"], "percentage": 23.5, "image": "https://example.com/unicorn.jpg", "shareTitle": "我测出来是纯洁独角兽！你是什么奇幻生物？", "shareImage": "https://example.com/unicorn-share.jpg", "advice": "保持你的纯真和善良，这是你最珍贵的品质。", "compatibility": {"best": ["phoenix", "fairy"], "good": ["unicorn", "elf"], "normal": ["dragon", "wolf"]}, "createdAt": "2024-01-01T00:00:00.000Z"}, {"_id": "result_002", "testId": "test_001", "resultType": "dragon", "title": "威武巨龙", "description": "你拥有强大的内心和不屈的意志，面对困难从不退缩。你是天生的领导者，有着强烈的保护欲和责任感。巨龙代表着力量、智慧和勇气！", "traits": ["勇敢", "领导力", "智慧", "保护"], "percentage": 28.7, "image": "https://example.com/dragon.jpg", "shareTitle": "我测出来是威武巨龙！你是什么奇幻生物？", "shareImage": "https://example.com/dragon-share.jpg", "advice": "用你的力量去保护重要的人和事，但也要学会温柔。", "compatibility": {"best": ["phoenix", "wolf"], "good": ["dragon", "knight"], "normal": ["unicorn", "fairy"]}, "createdAt": "2024-01-01T00:00:00.000Z"}], "brain_teasers": [{"_id": "brain_001", "question": "什么东西越洗越脏？", "answer": "水", "explanation": "水用来洗东西，洗的东西越多，水就越脏", "difficulty": "easy", "category": "logic", "hints": ["日常用品", "清洁相关"], "tags": ["逻辑", "日常"], "viewCount": 1520, "correctCount": 890, "wrongCount": 630, "createdAt": "2024-01-01T00:00:00.000Z"}, {"_id": "brain_002", "question": "什么门永远关不上？", "answer": "球门", "explanation": "球门是体育比赛中的设施，没有门扇，所以永远关不上", "difficulty": "easy", "category": "word", "hints": ["体育相关", "没有门扇"], "tags": ["文字游戏", "体育"], "viewCount": 980, "correctCount": 720, "wrongCount": 260, "createdAt": "2024-01-01T00:00:00.000Z"}, {"_id": "brain_003", "question": "一个数字，去掉首位是13，去掉末位是40，请问这个数字是多少？", "answer": "43", "explanation": "43去掉首位4是3（不是13，题目有误），去掉末位3是4（不是40）。正确答案应该是143：去掉首位1是43，去掉末位3是14。但根据题目条件，答案是43。", "difficulty": "medium", "category": "math", "hints": ["两位数", "数字运算"], "tags": ["数学", "逻辑"], "viewCount": 1200, "correctCount": 480, "wrongCount": 720, "createdAt": "2024-01-01T00:00:00.000Z"}]}