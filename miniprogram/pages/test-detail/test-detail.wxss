/* test-detail.wxss */

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 测试封面 */
.cover-section {
  position: relative;
  height: 500rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: flex-end;
}

.cover-content {
  padding: 40rpx 30rpx;
  color: white;
  width: 100%;
}

.test-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  line-height: 1.3;
}

.test-desc {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 25rpx;
  line-height: 1.5;
}

.test-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 测试信息 */
.info-section {
  padding: 30rpx;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.difficulty-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.difficulty-easy {
  background: #e8f5e8;
  color: #52c41a;
}

.difficulty-medium {
  background: #fff7e6;
  color: #fa8c16;
}

.difficulty-hard {
  background: #fff2f0;
  color: #f5222d;
}

.info-content {
  margin-bottom: 25rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.info-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666;
}

/* 测试说明 */
.instruction-section {
  padding: 0 30rpx 30rpx;
}

.instruction-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}

.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.instruction-icon {
  font-size: 28rpx;
  flex-shrink: 0;
}

.instruction-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 历史记录 */
.history-section {
  padding: 0 30rpx 30rpx;
}

.history-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  color: white;
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 28rpx;
  font-weight: bold;
}

.history-date {
  font-size: 24rpx;
  opacity: 0.8;
}

.history-result {
  text-align: center;
}

.result-type {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.result-desc {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 操作按钮 */
.action-section {
  padding: 30rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-buttons .btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.btn-icon {
  font-size: 28rpx;
}

/* 相关推荐 */
.recommend-section {
  padding: 0 30rpx 30rpx;
}

.recommend-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  vertical-align: top;
}

.recommend-cover {
  width: 100%;
  height: 200rpx;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recommend-count {
  font-size: 22rpx;
  color: #999;
}
