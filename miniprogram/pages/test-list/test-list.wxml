<!--test-list.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-icon">🔍</view>
      <input class="search-input" placeholder="搜索测试..." value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearchConfirm" />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onClearSearch">✕</view>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
      <view class="filter-item {{currentType === '' ? 'active' : ''}}" bindtap="onFilterTap" data-type="">
        全部
      </view>
      <view class="filter-item {{currentType === 'personality' ? 'active' : ''}}" bindtap="onFilterTap" data-type="personality">
        性格测试
      </view>
      <view class="filter-item {{currentType === 'mbti' ? 'active' : ''}}" bindtap="onFilterTap" data-type="mbti">
        MBTI
      </view>
      <view class="filter-item {{currentType === 'fantasy_creature' ? 'active' : ''}}" bindtap="onFilterTap" data-type="fantasy_creature">
        奇幻生物
      </view>
      <view class="filter-item {{currentType === 'career' ? 'active' : ''}}" bindtap="onFilterTap" data-type="career">
        职场测试
      </view>
      <view class="filter-item {{currentType === 'love_match' ? 'active' : ''}}" bindtap="onFilterTap" data-type="love_match">
        恋爱匹配
      </view>
    </scroll-view>
  </view>

  <!-- 排序选项 -->
  <view class="sort-section">
    <view class="sort-item {{sortBy === 'hot' ? 'active' : ''}}" bindtap="onSortTap" data-sort="hot">
      🔥 热门
    </view>
    <view class="sort-item {{sortBy === 'new' ? 'active' : ''}}" bindtap="onSortTap" data-sort="new">
      🆕 最新
    </view>
    <view class="sort-item {{sortBy === 'popular' ? 'active' : ''}}" bindtap="onSortTap" data-sort="popular">
      👍 好评
    </view>
  </view>

  <!-- 测试列表 -->
  <view class="test-list">
    <view class="test-card" wx:for="{{testList}}" wx:key="_id" bindtap="onTestTap" data-test="{{item}}">
      <image class="test-cover" src="{{item.coverImage}}" mode="aspectFill" lazy-load="true"></image>
      <view class="test-info">
        <view class="test-title">{{item.title}}</view>
        <view class="test-desc">{{item.description}}</view>
        <view class="test-meta">
          <view class="meta-item">
            <text class="meta-icon">👥</text>
            <text class="meta-text">{{item.completionCount}}人测过</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">⏱</text>
            <text class="meta-text">{{item.estimatedTime}}分钟</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">❤️</text>
            <text class="meta-text">{{item.likeCount}}</text>
          </view>
        </view>
        <view class="test-tags">
          <text class="tag difficulty-{{item.difficulty}}">{{item.difficulty === 'easy' ? '简单' : item.difficulty === 'medium' ? '中等' : '困难'}}</text>
          <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
      </view>
      <view class="test-badges">
        <view class="badge hot" wx:if="{{item.isHot}}">🔥</view>
        <view class="badge recommend" wx:if="{{item.isRecommended}}">⭐</view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <view class="load-more-btn" bindtap="onLoadMore">加载更多</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>{{testList.length > 0 ? '加载更多中...' : '加载中...'}}</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && testList.length === 0}}">
    <view class="empty-icon">🔍</view>
    <view class="empty-title">暂无测试</view>
    <view class="empty-desc">{{searchKeyword ? '换个关键词试试吧' : '敬请期待更多精彩测试'}}</view>
  </view>
</view>
