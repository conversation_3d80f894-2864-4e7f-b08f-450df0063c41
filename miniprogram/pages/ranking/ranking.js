// ranking.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: false,
    currentTab: 'total',
    rankingList: [],
    myRank: null
  },

  onLoad() {
    this.loadRanking()
  },

  onPullDownRefresh() {
    this.loadRanking().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 标签切换
   */
  onTabTap(e) {
    const tab = e.currentTarget.dataset.tab
    if (tab === this.data.currentTab) return

    this.setData({ currentTab: tab })
    this.loadRanking()
  },

  /**
   * 加载排行榜
   */
  async loadRanking() {
    try {
      this.setData({ loading: true })

      // 模拟数据
      const mockData = {
        list: [
          {
            _id: '1',
            nickName: '测试达人',
            avatarUrl: '/images/default-avatar.png',
            points: 1250,
            completedTests: 25,
            level: 5
          },
          {
            _id: '2',
            nickName: '智慧之星',
            avatarUrl: '/images/default-avatar.png',
            points: 980,
            completedTests: 20,
            level: 4
          },
          {
            _id: '3',
            nickName: '好奇宝宝',
            avatarUrl: '/images/default-avatar.png',
            points: 750,
            completedTests: 15,
            level: 3
          }
        ],
        myRank: {
          rank: 15,
          points: 320,
          nickName: '我',
          avatarUrl: '/images/default-avatar.png'
        }
      }

      this.setData({
        rankingList: mockData.list,
        myRank: mockData.myRank
      })

    } catch (error) {
      console.error('加载排行榜失败:', error)
      util.showError('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    return {
      title: '排行榜 - 看看谁是测试达人！',
      path: '/pages/ranking/ranking',
      imageUrl: '/images/share-ranking.jpg'
    }
  }
})
