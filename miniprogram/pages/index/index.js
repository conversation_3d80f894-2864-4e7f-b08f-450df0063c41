// index.js
const app = getApp()
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: true,
    banners: [],
    quickEntries: [
      {
        type: 'personality',
        name: '性格测试',
        icon: '🧠',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        count: 15
      },
      {
        type: 'mbti',
        name: 'MB<PERSON>',
        icon: '🎭',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        count: 8
      },
      {
        type: 'fantasy_creature',
        name: '奇幻生物',
        icon: '🦄',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        count: 12
      },
      {
        type: 'career',
        name: '职场测试',
        icon: '💼',
        color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        count: 10
      }
    ],
    hotTests: [],
    todayRecommend: null
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadHotTests()
  },

  onPullDownRefresh() {
    this.initPage().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      this.setData({ loading: true })

      // 并行加载数据
      await Promise.all([
        this.loadBanners(),
        this.loadHotTests(),
        this.loadTodayRecommend()
      ])

    } catch (error) {
      console.error('初始化页面失败:', error)
      util.showError('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载轮播图数据
   */
  async loadBanners() {
    // 模拟轮播图数据，实际项目中从后台获取
    const banners = [
      {
        id: 1,
        title: '测测你是哪种奇幻生物？',
        description: '超准的性格测试，发现你的神秘属性！',
        image: '/images/banner1.jpg',
        testId: 'test_001'
      },
      {
        id: 2,
        title: '职场人格大揭秘',
        description: '了解你在职场中的真实性格',
        image: '/images/banner2.jpg',
        testId: 'test_002'
      },
      {
        id: 3,
        title: 'MBTI趣味版',
        description: '用有趣的方式重新认识自己',
        image: '/images/banner3.jpg',
        testId: 'test_003'
      }
    ]

    this.setData({ banners })
  },

  /**
   * 加载热门测试
   */
  async loadHotTests() {
    try {
      const result = await api.getTestList({
        sortBy: 'hot',
        pageSize: 6
      })

      this.setData({
        hotTests: result.list || []
      })
    } catch (error) {
      console.error('加载热门测试失败:', error)
    }
  },

  /**
   * 加载今日推荐
   */
  async loadTodayRecommend() {
    try {
      const result = await api.getTestList({
        sortBy: 'popular',
        pageSize: 1
      })

      if (result.list && result.list.length > 0) {
        this.setData({
          todayRecommend: result.list[0]
        })
      }
    } catch (error) {
      console.error('加载今日推荐失败:', error)
    }
  },

  /**
   * 轮播图点击事件
   */
  onBannerTap(e) {
    const item = e.currentTarget.dataset.item
    if (item.testId) {
      wx.navigateTo({
        url: `/pages/test-detail/test-detail?id=${item.testId}`
      })
    }
  },

  /**
   * 快速入口点击事件
   */
  onQuickEntryTap(e) {
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/pages/test-list/test-list?type=${type}`
    })
  },

  /**
   * 测试卡片点击事件
   */
  onTestTap(e) {
    const test = e.currentTarget.dataset.test
    wx.navigateTo({
      url: `/pages/test-detail/test-detail?id=${test._id}`
    })
  },

  /**
   * 更多测试点击事件
   */
  onMoreTestsTap() {
    wx.switchTab({
      url: '/pages/test-list/test-list'
    })
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    return {
      title: '超有趣的性格测试，快来发现真实的自己！',
      path: '/pages/index/index',
      imageUrl: '/images/share-default.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '趣味测试 - 发现真实的自己',
      imageUrl: '/images/share-timeline.jpg'
    }
  }
});
