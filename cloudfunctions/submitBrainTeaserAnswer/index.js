// 提交脑筋急转弯答案云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { 
      questionId, 
      userAnswer, 
      hintsUsed = 0, 
      timeSpent = 0 
    } = event

    const { OPENID } = cloud.getWXContext()

    if (!questionId || !userAnswer) {
      return {
        success: false,
        message: '参数不完整'
      }
    }

    // 获取题目信息
    const questionResult = await db.collection('brain_teasers').doc(questionId).get()
    
    if (!questionResult.data) {
      return {
        success: false,
        message: '题目不存在'
      }
    }

    const question = questionResult.data

    // 检查用户是否已经答过这道题
    const existingRecordResult = await db.collection('user_brain_records')
      .where({
        userId: OPENID,
        questionId: questionId
      })
      .get()

    if (existingRecordResult.data.length > 0) {
      return {
        success: false,
        message: '您已经回答过这道题了'
      }
    }

    // 判断答案是否正确（简单的字符串匹配，可以扩展为更复杂的匹配逻辑）
    const correctAnswer = question.answer.toLowerCase().trim()
    const userAnswerLower = userAnswer.toLowerCase().trim()
    const isCorrect = correctAnswer === userAnswerLower || 
                     correctAnswer.includes(userAnswerLower) ||
                     userAnswerLower.includes(correctAnswer)

    // 计算获得积分
    let pointsEarned = 0
    if (isCorrect) {
      pointsEarned = 3 // 答对基础分
      if (hintsUsed === 0) {
        pointsEarned += 2 // 未使用提示额外分
      }
      if (timeSpent <= 30) {
        pointsEarned += 1 // 快速答题额外分
      }
    } else {
      pointsEarned = 1 // 答错安慰分
    }

    // 保存用户答题记录
    const userRecord = {
      userId: OPENID,
      questionId: questionId,
      userAnswer: userAnswer,
      isCorrect: isCorrect,
      hintsUsed: hintsUsed,
      timeSpent: timeSpent,
      pointsEarned: pointsEarned,
      answeredAt: new Date()
    }

    await db.collection('user_brain_records').add({
      data: userRecord
    })

    // 更新题目统计
    const updateData = {}
    if (isCorrect) {
      updateData.correctCount = db.command.inc(1)
    } else {
      updateData.wrongCount = db.command.inc(1)
    }

    await db.collection('brain_teasers').doc(questionId).update({
      data: updateData
    })

    // 更新用户积分和统计
    const userResult = await db.collection('users').where({
      openId: OPENID
    }).get()

    if (userResult.data.length > 0) {
      await db.collection('users').doc(userResult.data[0]._id).update({
        data: {
          totalPoints: db.command.inc(pointsEarned)
        }
      })
    } else {
      // 如果用户不存在，创建用户记录
      await db.collection('users').add({
        data: {
          openId: OPENID,
          totalPoints: pointsEarned,
          completedTests: 0,
          sharedCount: 0,
          invitedFriends: 0,
          consecutiveCheckIn: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    }

    return {
      success: true,
      data: {
        isCorrect: isCorrect,
        correctAnswer: question.answer,
        explanation: question.explanation,
        pointsEarned: pointsEarned,
        userAnswer: userAnswer,
        stats: {
          correctCount: question.correctCount + (isCorrect ? 1 : 0),
          wrongCount: question.wrongCount + (isCorrect ? 0 : 1)
        }
      }
    }

  } catch (error) {
    console.error('提交脑筋急转弯答案失败:', error)
    return {
      success: false,
      message: '提交答案失败',
      error: error.message
    }
  }
}
