// 获取好友匹配度云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { friendOpenId, testId } = event
    const { OPENID } = cloud.getWXContext()

    if (!friendOpenId || !testId) {
      return {
        success: false,
        message: '参数不完整'
      }
    }

    // 获取当前用户的测试结果
    const userRecordResult = await db.collection('user_test_records')
      .where({
        userId: OPENID,
        testId: testId
      })
      .orderBy('completedAt', 'desc')
      .limit(1)
      .get()

    if (userRecordResult.data.length === 0) {
      return {
        success: false,
        message: '你还没有完成这个测试'
      }
    }

    // 获取好友的测试结果
    const friendRecordResult = await db.collection('user_test_records')
      .where({
        userId: friendOpenId,
        testId: testId
      })
      .orderBy('completedAt', 'desc')
      .limit(1)
      .get()

    if (friendRecordResult.data.length === 0) {
      return {
        success: false,
        message: '你的好友还没有完成这个测试'
      }
    }

    const userRecord = userRecordResult.data[0]
    const friendRecord = friendRecordResult.data[0]

    // 计算匹配度
    const matchScore = calculateMatchScore(userRecord.result, friendRecord.result)

    // 保存好友关系记录
    const relationRecord = {
      userId: OPENID,
      friendId: friendOpenId,
      relationshipType: 'match',
      testId: testId,
      matchScore: matchScore,
      userResult: userRecord.result,
      friendResult: friendRecord.result,
      createdAt: new Date()
    }

    await db.collection('friend_relations').add({
      data: relationRecord
    })

    // 获取匹配度描述
    const matchDescription = getMatchDescription(matchScore)

    return {
      success: true,
      data: {
        matchScore: matchScore,
        matchDescription: matchDescription,
        userResult: userRecord.result,
        friendResult: friendRecord.result,
        compatibility: getCompatibilityAnalysis(userRecord.result.type, friendRecord.result.type)
      }
    }

  } catch (error) {
    console.error('获取好友匹配度失败:', error)
    return {
      success: false,
      message: '获取匹配度失败',
      error: error.message
    }
  }
}

/**
 * 计算匹配度分数
 */
function calculateMatchScore(userResult, friendResult) {
  // 如果是相同类型，基础匹配度较高
  if (userResult.type === friendResult.type) {
    return Math.floor(Math.random() * 20) + 80 // 80-100
  }

  // 根据兼容性计算匹配度
  const compatibility = getTypeCompatibility(userResult.type, friendResult.type)
  
  switch (compatibility) {
    case 'best':
      return Math.floor(Math.random() * 25) + 75 // 75-100
    case 'good':
      return Math.floor(Math.random() * 30) + 50 // 50-80
    case 'normal':
      return Math.floor(Math.random() * 40) + 30 // 30-70
    default:
      return Math.floor(Math.random() * 50) + 25 // 25-75
  }
}

/**
 * 获取类型兼容性
 */
function getTypeCompatibility(type1, type2) {
  const compatibilityMap = {
    'unicorn': {
      'phoenix': 'best',
      'fairy': 'best',
      'elf': 'good',
      'dragon': 'normal',
      'wolf': 'normal'
    },
    'dragon': {
      'phoenix': 'best',
      'wolf': 'best',
      'knight': 'good',
      'unicorn': 'normal',
      'fairy': 'normal'
    },
    'phoenix': {
      'unicorn': 'best',
      'dragon': 'best',
      'elf': 'good',
      'wolf': 'normal',
      'fairy': 'good'
    },
    'wolf': {
      'dragon': 'best',
      'knight': 'good',
      'phoenix': 'normal',
      'unicorn': 'normal',
      'fairy': 'normal'
    }
  }

  return compatibilityMap[type1]?.[type2] || 'normal'
}

/**
 * 获取匹配度描述
 */
function getMatchDescription(score) {
  if (score >= 90) {
    return '天作之合！你们简直是完美搭配！'
  } else if (score >= 80) {
    return '超级匹配！你们有很多共同点！'
  } else if (score >= 70) {
    return '很不错的搭配，你们能互相理解！'
  } else if (score >= 60) {
    return '还不错的组合，有一定的默契！'
  } else if (score >= 50) {
    return '普通朋友，需要多多磨合！'
  } else {
    return '差异较大，但正好可以互补！'
  }
}

/**
 * 获取兼容性分析
 */
function getCompatibilityAnalysis(type1, type2) {
  const analysisMap = {
    'unicorn-phoenix': '纯洁与重生的完美结合，你们能给彼此带来希望和力量！',
    'dragon-wolf': '力量与野性的强强联合，你们是最佳的冒险伙伴！',
    'unicorn-dragon': '善良与力量的平衡，你们能互相学习和成长！'
  }

  const key1 = `${type1}-${type2}`
  const key2 = `${type2}-${type1}`
  
  return analysisMap[key1] || analysisMap[key2] || '你们各有特色，能够互相补充！'
}
