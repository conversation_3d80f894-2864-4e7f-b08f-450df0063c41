// test-detail.js
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    loading: true,
    testId: '',
    testInfo: {},
    userRecord: null,
    recommendTests: []
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        testId: options.id
      })
      this.loadTestDetail()
    } else {
      util.showError('测试ID不能为空')
      wx.navigateBack()
    }
  },

  onShow() {
    // 检查是否有新的测试记录
    this.checkUserRecord()
  },

  /**
   * 加载测试详情
   */
  async loadTestDetail() {
    try {
      this.setData({ loading: true })

      // 并行加载数据
      const [testDetail, userRecord, recommendTests] = await Promise.all([
        api.getTestDetail(this.data.testId),
        this.getUserRecord(),
        this.getRecommendTests()
      ])

      this.setData({
        testInfo: testDetail,
        userRecord: userRecord,
        recommendTests: recommendTests
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: testDetail.title
      })

    } catch (error) {
      console.error('加载测试详情失败:', error)
      util.showError('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 获取用户测试记录
   */
  async getUserRecord() {
    try {
      const result = await api.getUserTestHistory(1, 1)
      const records = result.list || []
      return records.find(record => record.testId === this.data.testId) || null
    } catch (error) {
      console.error('获取用户记录失败:', error)
      return null
    }
  },

  /**
   * 获取推荐测试
   */
  async getRecommendTests() {
    try {
      const result = await api.getTestList({
        category: this.data.testInfo.category,
        pageSize: 5
      })
      // 排除当前测试
      return (result.list || []).filter(test => test._id !== this.data.testId)
    } catch (error) {
      console.error('获取推荐测试失败:', error)
      return []
    }
  },

  /**
   * 检查用户记录
   */
  async checkUserRecord() {
    const userRecord = await this.getUserRecord()
    this.setData({ userRecord })
  },

  /**
   * 开始测试
   */
  onStartTest() {
    // 如果用户已经测试过，询问是否重新测试
    if (this.data.userRecord) {
      util.showConfirm('你已经测试过了，是否重新测试？').then(confirmed => {
        if (confirmed) {
          this.navigateToTest()
        }
      })
    } else {
      this.navigateToTest()
    }
  },

  /**
   * 跳转到测试页面
   */
  navigateToTest() {
    wx.navigateTo({
      url: `/pages/test-question/test-question?id=${this.data.testId}`
    })
  },

  /**
   * 分享测试
   */
  onShareTest() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 推荐测试点击
   */
  onRecommendTap(e) {
    const test = e.currentTarget.dataset.test
    wx.redirectTo({
      url: `/pages/test-detail/test-detail?id=${test._id}`
    })
  },

  /**
   * 查看历史记录
   */
  onHistoryTap() {
    if (this.data.userRecord) {
      wx.navigateTo({
        url: `/pages/test-result/test-result?recordId=${this.data.userRecord._id}`
      })
    }
  },

  /**
   * 分享配置
   */
  onShareAppMessage() {
    const testInfo = this.data.testInfo
    return {
      title: `${testInfo.title} - 快来测测你的结果吧！`,
      path: `/pages/test-detail/test-detail?id=${this.data.testId}`,
      imageUrl: testInfo.coverImage || '/images/share-default.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const testInfo = this.data.testInfo
    return {
      title: `${testInfo.title} - 趣味测试`,
      imageUrl: testInfo.coverImage || '/images/share-timeline.jpg'
    }
  }
})
