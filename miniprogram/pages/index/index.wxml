<!--index.wxml-->
<view class="container">
  <!-- 顶部轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-item="{{item}}"></image>
        <view class="banner-overlay">
          <view class="banner-title">{{item.title}}</view>
          <view class="banner-desc">{{item.description}}</view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快速入口 -->
  <view class="quick-entry-section">
    <view class="section-title">
      <text class="title-text">热门分类</text>
      <text class="title-icon">🔥</text>
    </view>
    <view class="quick-entry-grid">
      <view class="quick-entry-item" wx:for="{{quickEntries}}" wx:key="type" bindtap="onQuickEntryTap" data-type="{{item.type}}">
        <view class="entry-icon" style="background: {{item.color}};">{{item.icon}}</view>
        <text class="entry-name">{{item.name}}</text>
        <text class="entry-count">{{item.count}}个测试</text>
      </view>
    </view>
  </view>

  <!-- 热门测试 -->
  <view class="hot-tests-section">
    <view class="section-title">
      <text class="title-text">热门测试</text>
      <text class="more-btn" bindtap="onMoreTestsTap">更多 ></text>
    </view>
    <view class="test-list">
      <view class="test-card" wx:for="{{hotTests}}" wx:key="_id" bindtap="onTestTap" data-test="{{item}}">
        <image class="test-cover" src="{{item.coverImage}}" mode="aspectFill"></image>
        <view class="test-info">
          <view class="test-title">{{item.title}}</view>
          <view class="test-desc">{{item.description}}</view>
          <view class="test-meta">
            <view class="meta-item">
              <text class="meta-icon">👥</text>
              <text class="meta-text">{{item.completionCount}}人测过</text>
            </view>
            <view class="meta-item">
              <text class="meta-icon">⏱</text>
              <text class="meta-text">{{item.estimatedTime}}分钟</text>
            </view>
          </view>
          <view class="test-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
        <view class="test-badge" wx:if="{{item.isHot}}">🔥</view>
      </view>
    </view>
  </view>

  <!-- 今日推荐 -->
  <view class="recommend-section">
    <view class="section-title">
      <text class="title-text">今日推荐</text>
      <text class="title-icon">⭐</text>
    </view>
    <view class="recommend-card" wx:if="{{todayRecommend}}" bindtap="onTestTap" data-test="{{todayRecommend}}">
      <image class="recommend-bg" src="{{todayRecommend.coverImage}}" mode="aspectFill"></image>
      <view class="recommend-overlay">
        <view class="recommend-content">
          <view class="recommend-title">{{todayRecommend.title}}</view>
          <view class="recommend-desc">{{todayRecommend.description}}</view>
          <view class="recommend-btn">立即测试</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</view>