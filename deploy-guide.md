# 趣味测试小程序部署指南

## 📋 部署前准备

### 1. 环境要求
- 微信开发者工具
- 已开通微信云开发
- Node.js 环境

### 2. 获取项目
```bash
# 克隆或下载项目到本地
# 使用微信开发者工具打开项目
```

## 🚀 快速部署步骤

### 第一步：配置云开发环境

1. 在微信开发者工具中打开项目
2. 点击"云开发"按钮，创建云开发环境
3. 记录环境ID，例如：`cloud1-xxx`

### 第二步：修改配置文件

1. 打开 `miniprogram/utils/config.js`
2. 修改 `cloudEnvId` 为你的环境ID：
```javascript
cloudEnvId: 'your-cloud-env-id', // 替换为你的环境ID
```

### 第三步：部署云函数

在微信开发者工具中，右键点击以下云函数文件夹，选择"上传并部署：云端安装依赖"：

- `cloudfunctions/getTestList`
- `cloudfunctions/getTestQuestions`
- `cloudfunctions/getTestResult`
- `cloudfunctions/generatePoster`
- `cloudfunctions/getBrainTeaserQuestion`
- `cloudfunctions/submitBrainTeaserAnswer`
- `cloudfunctions/recordShare`
- `cloudfunctions/getFriendMatch`

### 第四步：初始化数据库

1. 在云开发控制台中创建以下集合：
   - `tests` (测试表)
   - `questions` (题目表)
   - `test_results` (测试结果表)
   - `users` (用户表)
   - `user_test_records` (用户测试记录表)
   - `brain_teasers` (脑筋急转弯表)
   - `user_brain_records` (用户脑筋急转弯记录表)
   - `share_records` (分享记录表)
   - `friend_relations` (好友关系表)

2. 导入示例数据：
   - 将 `sample-data.json` 中的数据导入到对应集合中

### 第五步：配置云存储

1. 在云开发控制台中开启云存储
2. 上传默认图片资源到云存储
3. 更新代码中的图片URL

## 🎨 自定义配置

### 修改测试内容

1. **添加新测试**：
   - 在 `tests` 集合中添加测试信息
   - 在 `questions` 集合中添加对应题目
   - 在 `test_results` 集合中添加结果类型

2. **修改UI样式**：
   - 全局样式：`miniprogram/app.wxss`
   - 页面样式：各页面的 `.wxss` 文件

3. **添加新功能**：
   - 参考现有页面结构
   - 在 `app.json` 中注册新页面

### 海报生成配置

由于微信云开发的限制，海报生成功能需要：

1. **方案一**：集成第三方图片处理服务
   - 推荐使用腾讯云、阿里云等图片处理API
   - 修改 `generatePoster` 云函数

2. **方案二**：使用Canvas在小程序端生成
   - 在前端使用Canvas API绘制海报
   - 参考微信小程序Canvas文档

## 📱 功能特性

### 已实现功能
- ✅ 测试列表与分类
- ✅ 答题流程与进度
- ✅ 结果展示与分析
- ✅ 脑筋急转弯
- ✅ 用户系统与积分
- ✅ 分享功能
- ✅ 排行榜
- ✅ 个人中心

### 扩展功能建议
- 🔄 更多测试类型
- 🎨 海报模板系统
- 👥 社交功能增强
- 🏆 成就系统
- 📊 数据统计
- 🎯 个性化推荐

## 🔧 常见问题

### Q: 云函数部署失败？
A: 检查网络连接，确保云开发环境已开通，重试部署。

### Q: 数据库连接失败？
A: 确认环境ID配置正确，检查云开发权限设置。

### Q: 海报生成不工作？
A: 需要集成第三方服务或使用Canvas实现，参考上述配置说明。

### Q: 如何添加新的测试？
A: 按照数据库设计文档，在对应集合中添加数据。

## 📞 技术支持

如有问题，请参考：
- 微信小程序官方文档
- 微信云开发文档
- 项目中的代码注释

## 📄 许可证

本项目仅供学习和参考使用，请遵守相关法律法规。
